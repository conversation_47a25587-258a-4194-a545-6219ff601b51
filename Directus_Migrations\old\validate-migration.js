#!/usr/bin/env node

/**
 * Comprehensive Migration Validation Script
 * Validates that the Directus schema migration was successful
 * 
 * Usage: node scripts/validate-migration.js [environment]
 */

const { createDirectus, rest, authentication, staticToken, readCollections, readFields, readRelations, readItems, createItem } = require('@directus/sdk');
const fs = require('fs').promises;

// Configuration (same as migration script)
const CONFIG = {
  development: {
    url: 'http://localhost:8055',
    email: '<EMAIL>',
    password: 'Mb123321',
    token: null
  },
  staging: {
    url: process.env.DIRECTUS_STAGING_URL || 'https://staging-directus.yourdomain.com',
    email: process.env.DIRECTUS_STAGING_EMAIL,
    password: process.env.DIRECTUS_STAGING_PASSWORD,
    token: process.env.DIRECTUS_STAGING_TOKEN
  },
  production: {
    url: process.env.DIRECTUS_PROD_URL || 'https://directus.yourdomain.com',
    email: process.env.DIRECTUS_PROD_EMAIL,
    password: process.env.DIRECTUS_PROD_PASSWORD,
    token: process.env.DIRECTUS_PROD_TOKEN
  }
};

class MigrationValidator {
  constructor(environment = 'development') {
    this.environment = environment;
    this.config = CONFIG[environment];
    this.directus = null;
    this.validationResults = {
      collections: { passed: 0, failed: 0, details: [] },
      fields: { passed: 0, failed: 0, details: [] },
      relations: { passed: 0, failed: 0, details: [] },
      performance: { passed: 0, failed: 0, details: [] },
      businessLogic: { passed: 0, failed: 0, details: [] }
    };
  }

  async initialize() {
    console.log(`🔍 Initializing validation for ${this.environment} environment...`);
    
    this.directus = createDirectus(this.config.url).with(rest());
    
    if (this.config.token) {
      this.directus = this.directus.with(staticToken(this.config.token));
    } else if (this.config.email && this.config.password) {
      this.directus = this.directus.with(authentication());
      await this.directus.login(this.config.email, this.config.password);
    } else {
      throw new Error('No authentication method configured');
    }

    console.log('✅ Connected to Directus');
  }

  async validateCollections() {
    console.log('\n📁 Validating Collections...');
    
    const expectedCollections = [
      'properties',
      'projects', 
      'memberships',
      'messages',
      'notifications',
      'nearby_place_categories',
      'properties_files',
      'messages_files'
    ];

    try {
      const collections = await this.directus.request(readCollections());

      const existingCollections = collections.map(c => c.collection);

      for (const expectedCollection of expectedCollections) {
        if (existingCollections.includes(expectedCollection)) {
          this.validationResults.collections.passed++;
          this.validationResults.collections.details.push({
            type: 'success',
            message: `Collection '${expectedCollection}' exists`
          });
          console.log(`  ✅ ${expectedCollection}`);
        } else {
          this.validationResults.collections.failed++;
          this.validationResults.collections.details.push({
            type: 'error',
            message: `Collection '${expectedCollection}' missing`
          });
          console.log(`  ❌ ${expectedCollection} - MISSING`);
        }
      }

    } catch (error) {
      this.validationResults.collections.failed++;
      this.validationResults.collections.details.push({
        type: 'error',
        message: `Failed to fetch collections: ${error.message}`
      });
      console.log(`  ❌ Failed to fetch collections: ${error.message}`);
    }
  }

  async validateFields() {
    console.log('\n🔧 Validating Fields...');
    
    const criticalFields = {
      properties: [
        'id', 'title', 'description', 'price', 'currency', 'property_type',
        'offer', 'bedrooms', 'bathrooms', 'area', 'area_unit', 'address',
        'city', 'country', 'coordinates', 'nearby_places', 'features',
        'is_luxury', 'featured', 'views', 'slug', 'owner_id', 'agent_id',
        'project_id', 'status', 'date_created', 'date_updated'
      ],
      projects: [
        'id', 'title', 'description', 'developer', 'status', 'completion_date',
        'starting_price', 'currency', 'address', 'city', 'country',
        'latitude', 'longitude', 'amenities', 'virtual_tour', 'payment_plan',
        'featured', 'slug', 'date_created', 'date_updated'
      ],
      memberships: [
        'id', 'name', 'slug', 'description', 'price', 'currency', 'duration',
        'features', 'max_properties', 'max_images', 'can_create_projects',
        'can_access_analytics', 'can_use_premium_filters', 'priority',
        'is_active', 'status', 'date_created', 'date_updated'
      ],
      messages: [
        'id', 'subject', 'content', 'sender_id', 'recipient_id', 'property_id',
        'project_id', 'message_type', 'is_read', 'read_at', 'parent_message_id',
        'status', 'date_created', 'date_updated'
      ],
      notifications: [
        'id', 'title', 'message', 'type', 'is_read', 'read_at', 'recipient_id',
        'sender_id', 'related_property_id', 'related_project_id', 'related_message_id',
        'action_url', 'action_text', 'metadata', 'priority', 'expires_at',
        'status', 'date_created', 'date_updated'
      ],
      nearby_place_categories: [
        'id', 'name', 'display_name', 'description', 'icon', 'google_place_types',
        'enabled', 'search_radius', 'max_results', 'priority', 'color',
        'status', 'date_created', 'date_updated'
      ],
      directus_users: [
        'first_name', 'last_name', 'phone', 'company', 'job_title', 'bio',
        'is_agent', 'membership_id', 'membership_expires_at'
      ]
    };

    for (const [collectionName, expectedFields] of Object.entries(criticalFields)) {
      console.log(`  📝 Checking ${collectionName} fields...`);
      
      try {
        const fields = await this.directus.request(readFields(collectionName));

        const existingFields = fields.map(f => f.field);

        for (const expectedField of expectedFields) {
          if (existingFields.includes(expectedField)) {
            this.validationResults.fields.passed++;
            console.log(`    ✅ ${expectedField}`);
          } else {
            this.validationResults.fields.failed++;
            this.validationResults.fields.details.push({
              type: 'error',
              message: `Field '${collectionName}.${expectedField}' missing`
            });
            console.log(`    ❌ ${expectedField} - MISSING`);
          }
        }

      } catch (error) {
        this.validationResults.fields.failed++;
        this.validationResults.fields.details.push({
          type: 'error',
          message: `Failed to fetch fields for ${collectionName}: ${error.message}`
        });
        console.log(`    ❌ Failed to fetch fields: ${error.message}`);
      }
    }
  }

  async validateRelations() {
    console.log('\n🔗 Validating Relationships...');
    
    const expectedRelations = [
      { collection: 'properties', field: 'owner_id', related: 'directus_users' },
      { collection: 'properties', field: 'agent_id', related: 'directus_users' },
      { collection: 'properties', field: 'project_id', related: 'projects' },
      { collection: 'messages', field: 'sender_id', related: 'directus_users' },
      { collection: 'messages', field: 'recipient_id', related: 'directus_users' },
      { collection: 'messages', field: 'property_id', related: 'properties' },
      { collection: 'messages', field: 'project_id', related: 'projects' },
      { collection: 'notifications', field: 'recipient_id', related: 'directus_users' },
      { collection: 'notifications', field: 'sender_id', related: 'directus_users' },
      { collection: 'directus_users', field: 'membership_id', related: 'memberships' }
    ];

    try {
      const existingRelations = await this.directus.request(readRelations());

      for (const expectedRelation of expectedRelations) {
        const found = existingRelations.find(r => 
          r.collection === expectedRelation.collection && 
          r.field === expectedRelation.field &&
          r.related_collection === expectedRelation.related
        );

        if (found) {
          this.validationResults.relations.passed++;
          console.log(`  ✅ ${expectedRelation.collection}.${expectedRelation.field} → ${expectedRelation.related}`);
        } else {
          this.validationResults.relations.failed++;
          this.validationResults.relations.details.push({
            type: 'error',
            message: `Relation '${expectedRelation.collection}.${expectedRelation.field} → ${expectedRelation.related}' missing`
          });
          console.log(`  ❌ ${expectedRelation.collection}.${expectedRelation.field} → ${expectedRelation.related} - MISSING`);
        }
      }

    } catch (error) {
      this.validationResults.relations.failed++;
      this.validationResults.relations.details.push({
        type: 'error',
        message: `Failed to fetch relations: ${error.message}`
      });
      console.log(`  ❌ Failed to fetch relations: ${error.message}`);
    }
  }

  async validatePerformance() {
    console.log('\n⚡ Validating Performance...');
    
    const performanceTests = [
      {
        name: 'Basic collection access',
        test: async () => {
          const start = Date.now();
          await this.directus.request(readItems('properties', { limit: 1 }));
          return Date.now() - start;
        },
        threshold: 1000
      },
      {
        name: 'Relationship query',
        test: async () => {
          const start = Date.now();
          await this.directus.request(readItems('properties', {
            fields: ['*', { owner_id: ['first_name', 'last_name'] }],
            limit: 1
          }));
          return Date.now() - start;
        },
        threshold: 2000
      },
      {
        name: 'Complex filter query',
        test: async () => {
          const start = Date.now();
          await this.directus.request(readItems('properties', {
            filter: {
              property_type: { _eq: 'apartment' },
              price: { _gte: 100000 }
            },
            limit: 5
          }));
          return Date.now() - start;
        },
        threshold: 1500
      }
    ];

    for (const test of performanceTests) {
      try {
        const duration = await test.test();
        
        if (duration <= test.threshold) {
          this.validationResults.performance.passed++;
          console.log(`  ✅ ${test.name}: ${duration}ms (threshold: ${test.threshold}ms)`);
        } else {
          this.validationResults.performance.failed++;
          this.validationResults.performance.details.push({
            type: 'warning',
            message: `${test.name} took ${duration}ms (threshold: ${test.threshold}ms)`
          });
          console.log(`  ⚠️  ${test.name}: ${duration}ms (exceeds threshold: ${test.threshold}ms)`);
        }
      } catch (error) {
        this.validationResults.performance.failed++;
        this.validationResults.performance.details.push({
          type: 'error',
          message: `${test.name} failed: ${error.message}`
        });
        console.log(`  ❌ ${test.name} failed: ${error.message}`);
      }
    }
  }

  async validateBusinessLogic() {
    console.log('\n🧠 Validating Business Logic...');
    
    const businessLogicTests = [
      {
        name: 'Property price validation',
        test: async () => {
          try {
            await this.directus.request(createItem('properties', {
              title: 'Test Property',
              price: -100,
              property_type: 'apartment',
              offer: 'for-sale',
              area: 100,
              address: 'Test Address',
              city: 'Test City',
              country: 'Test Country',
              slug: 'test-property-validation'
            }));
            return false; // Should have failed
          } catch (error) {
            const errorMessage = error?.message || error?.toString() || '';
            return errorMessage.includes('validation') || errorMessage.includes('constraint');
          }
        }
      },
      {
        name: 'Unique slug constraint',
        test: async () => {
          try {
            // Try to create two properties with same slug
            const slug = `test-unique-${Date.now()}`;
            
            await this.directus.request(createItem('properties', {
              title: 'Test Property 1',
              price: 100000,
              property_type: 'apartment',
              offer: 'for-sale',
              area: 100,
              address: 'Test Address',
              city: 'Test City',
              country: 'Test Country',
              slug: slug
            }));

            await this.directus.request(createItem('properties', {
              title: 'Test Property 2',
              price: 200000,
              property_type: 'villa',
              offer: 'for-sale',
              area: 200,
              address: 'Test Address 2',
              city: 'Test City 2',
              country: 'Test Country 2',
              slug: slug
            }));
            
            return false; // Should have failed
          } catch (error) {
            const errorMessage = error?.message || error?.toString() || '';
            return errorMessage.includes('unique') || errorMessage.includes('duplicate');
          }
        }
      }
    ];

    for (const test of businessLogicTests) {
      try {
        const result = await test.test();
        
        if (result) {
          this.validationResults.businessLogic.passed++;
          console.log(`  ✅ ${test.name}`);
        } else {
          this.validationResults.businessLogic.failed++;
          this.validationResults.businessLogic.details.push({
            type: 'error',
            message: `${test.name} validation not working properly`
          });
          console.log(`  ❌ ${test.name} - validation not working`);
        }
      } catch (error) {
        this.validationResults.businessLogic.failed++;
        this.validationResults.businessLogic.details.push({
          type: 'error',
          message: `${test.name} test failed: ${error.message}`
        });
        console.log(`  ❌ ${test.name} test failed: ${error.message}`);
      }
    }
  }

  async generateReport() {
    console.log('\n📊 Validation Summary');
    console.log('='.repeat(50));
    
    const categories = ['collections', 'fields', 'relations', 'performance', 'businessLogic'];
    let totalPassed = 0;
    let totalFailed = 0;

    for (const category of categories) {
      const result = this.validationResults[category];
      const total = result.passed + result.failed;
      const percentage = total > 0 ? Math.round((result.passed / total) * 100) : 0;
      
      console.log(`${category.padEnd(20)}: ${result.passed}/${total} (${percentage}%)`);
      
      totalPassed += result.passed;
      totalFailed += result.failed;
    }

    const overallTotal = totalPassed + totalFailed;
    const overallPercentage = overallTotal > 0 ? Math.round((totalPassed / overallTotal) * 100) : 0;
    
    console.log('-'.repeat(50));
    console.log(`${'OVERALL'.padEnd(20)}: ${totalPassed}/${overallTotal} (${overallPercentage}%)`);
    
    // Save detailed report
    const reportPath = `logs/validation-report-${this.environment}-${Date.now()}.json`;
    await fs.mkdir('logs', { recursive: true });
    await fs.writeFile(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      environment: this.environment,
      summary: {
        totalPassed,
        totalFailed,
        overallPercentage
      },
      details: this.validationResults
    }, null, 2));

    console.log(`\n📋 Detailed report saved: ${reportPath}`);
    
    if (overallPercentage >= 90) {
      console.log('\n🎉 Migration validation PASSED! Schema is ready for use.');
      return true;
    } else {
      console.log('\n⚠️  Migration validation FAILED. Please review the issues above.');
      return false;
    }
  }

  async run() {
    try {
      await this.initialize();
      await this.validateCollections();
      await this.validateFields();
      await this.validateRelations();
      await this.validatePerformance();
      await this.validateBusinessLogic();
      
      const success = await this.generateReport();
      return success;
    } catch (error) {
      console.error('❌ Validation failed:', error.message);
      return false;
    }
  }
}

// Run validation if called directly
if (require.main === module) {
  const environment = process.argv[2] || 'development';
  
  const validator = new MigrationValidator(environment);
  
  validator.run()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Validation error:', error.message);
      process.exit(1);
    });
}

module.exports = MigrationValidator;
