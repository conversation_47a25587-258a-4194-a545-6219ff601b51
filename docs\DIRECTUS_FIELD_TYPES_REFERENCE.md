# Directus Field Types Reference Guide
## Complete Migration Mapping for Real Estate Platform

### Table of Contents
1. [Field Type Mapping Overview](#field-type-mapping-overview)
2. [Complete Collection Schemas](#complete-collection-schemas)
3. [Field Configuration Templates](#field-configuration-templates)
4. [Automation Scripts](#automation-scripts)
5. [Validation and Testing](#validation-and-testing)

---

## 1. Field Type Mapping Overview

### Strapi to Directus Field Type Mapping

| Strapi Type | Directus Type | Configuration Notes |
|-------------|---------------|-------------------|
| `string` | `string` | Use `validation.length` for maxLength |
| `text` | `text` | For long text content |
| `richtext` | `text` | Use interface `input-rich-text-html` |
| `integer` | `integer` | Use `validation.range` for min/max |
| `decimal` | `float` | Use `validation.range` for min/max |
| `boolean` | `boolean` | Use `schema.default_value` |
| `datetime` | `timestamp` | Use `special.date-created/date-updated` |
| `date` | `date` | Date only without time |
| `enumeration` | `string` | Use `validation.options` for choices |
| `json` | `json` | For complex data structures |
| `uid` | `string` | Use `validation.unique` + slug interface |
| `media` (single) | `file` | Use `validation.file_type` |
| `media` (multiple) | `files` | Use `validation.file_type` |
| `relation` (manyToOne) | `m2o` | Foreign key relationship |
| `relation` (oneToMany) | `o2m` | Reverse relationship |
| `relation` (manyToMany) | `m2m` | Junction table required |

---

## 2. Complete Collection Schemas

### 2.1 Properties Collection Schema

```json
{
  "collection": "properties",
  "meta": {
    "collection": "properties",
    "icon": "home",
    "note": "Real estate properties with all details",
    "display_template": "{{title}} - {{city}}",
    "hidden": false,
    "singleton": false,
    "translations": {},
    "archive_field": null,
    "archive_app_filter": true,
    "archive_value": null,
    "unarchive_value": null,
    "sort_field": "sort",
    "accountability": "all",
    "color": "#3B82F6",
    "item_duplication_fields": null,
    "sort": 1,
    "group": null,
    "collapse": "open"
  },
  "schema": {
    "name": "properties",
    "comment": null
  },
  "fields": [
    {
      "field": "id",
      "type": "uuid",
      "meta": {
        "hidden": true,
        "readonly": true,
        "interface": "input",
        "special": ["uuid"]
      },
      "schema": {
        "is_primary_key": true,
        "has_auto_increment": false,
        "is_nullable": false
      }
    },
    {
      "field": "title",
      "type": "string",
      "meta": {
        "interface": "input",
        "display": "formatted-value",
        "display_options": {
          "format": true
        },
        "required": true,
        "sort": 1,
        "width": "full",
        "translations": {},
        "note": "Property title/name"
      },
      "schema": {
        "name": "title",
        "table": "properties",
        "data_type": "varchar",
        "max_length": 255,
        "is_nullable": false,
        "is_unique": false,
        "has_auto_increment": false,
        "foreign_key_column": null,
        "foreign_key_table": null
      }
    },
    {
      "field": "description",
      "type": "text",
      "meta": {
        "interface": "input-rich-text-html",
        "display": "formatted-value",
        "sort": 2,
        "width": "full",
        "note": "Detailed property description"
      },
      "schema": {
        "name": "description",
        "table": "properties",
        "data_type": "text",
        "is_nullable": true
      }
    },
    {
      "field": "price",
      "type": "float",
      "meta": {
        "interface": "input",
        "display": "formatted-value",
        "display_options": {
          "decimals": 2,
          "prefix": "$"
        },
        "required": true,
        "sort": 3,
        "width": "half",
        "validation": {
          "_and": [
            {
              "price": {
                "_gte": 0
              }
            }
          ]
        },
        "note": "Property price"
      },
      "schema": {
        "name": "price",
        "table": "properties",
        "data_type": "decimal",
        "numeric_precision": 15,
        "numeric_scale": 2,
        "is_nullable": false
      }
    },
    {
      "field": "currency",
      "type": "string",
      "meta": {
        "interface": "select-dropdown",
        "display": "labels",
        "display_options": {
          "choices": [
            {"text": "US Dollar", "value": "USD"},
            {"text": "Euro", "value": "EUR"},
            {"text": "British Pound", "value": "GBP"},
            {"text": "UAE Dirham", "value": "AED"},
            {"text": "Saudi Riyal", "value": "SAR"}
          ]
        },
        "sort": 4,
        "width": "half",
        "validation": {
          "_in": ["USD", "EUR", "GBP", "AED", "SAR"]
        }
      },
      "schema": {
        "name": "currency",
        "table": "properties",
        "data_type": "varchar",
        "max_length": 3,
        "is_nullable": true,
        "default_value": "USD"
      }
    },
    {
      "field": "property_type",
      "type": "string",
      "meta": {
        "interface": "select-dropdown",
        "display": "labels",
        "display_options": {
          "choices": [
            {"text": "Apartment", "value": "apartment"},
            {"text": "Villa", "value": "villa"},
            {"text": "Townhouse", "value": "townhouse"},
            {"text": "Penthouse", "value": "penthouse"},
            {"text": "Studio", "value": "studio"},
            {"text": "Duplex", "value": "duplex"},
            {"text": "Land", "value": "land"},
            {"text": "Commercial", "value": "commercial"}
          ]
        },
        "required": true,
        "sort": 5,
        "width": "half",
        "validation": {
          "_in": ["apartment", "villa", "townhouse", "penthouse", "studio", "duplex", "land", "commercial"]
        }
      },
      "schema": {
        "name": "property_type",
        "table": "properties",
        "data_type": "varchar",
        "max_length": 50,
        "is_nullable": false
      }
    },
    {
      "field": "offer",
      "type": "string",
      "meta": {
        "interface": "select-dropdown",
        "display": "labels",
        "display_options": {
          "choices": [
            {"text": "For Sale", "value": "for-sale"},
            {"text": "For Rent", "value": "for-rent"},
            {"text": "Sold", "value": "sold"},
            {"text": "Rented", "value": "rented"},
            {"text": "Off Market", "value": "off-market"}
          ]
        },
        "required": true,
        "sort": 6,
        "width": "half",
        "validation": {
          "_in": ["for-sale", "for-rent", "sold", "rented", "off-market"]
        }
      },
      "schema": {
        "name": "offer",
        "table": "properties",
        "data_type": "varchar",
        "max_length": 50,
        "is_nullable": false
      }
    },
    {
      "field": "bedrooms",
      "type": "integer",
      "meta": {
        "interface": "input",
        "display": "formatted-value",
        "sort": 7,
        "width": "half",
        "validation": {
          "_and": [
            {
              "bedrooms": {
                "_gte": 0
              }
            },
            {
              "bedrooms": {
                "_lte": 20
              }
            }
          ]
        }
      },
      "schema": {
        "name": "bedrooms",
        "table": "properties",
        "data_type": "integer",
        "is_nullable": true,
        "default_value": 0
      }
    },
    {
      "field": "bathrooms",
      "type": "integer",
      "meta": {
        "interface": "input",
        "display": "formatted-value",
        "sort": 8,
        "width": "half",
        "validation": {
          "_and": [
            {
              "bathrooms": {
                "_gte": 0
              }
            },
            {
              "bathrooms": {
                "_lte": 20
              }
            }
          ]
        }
      },
      "schema": {
        "name": "bathrooms",
        "table": "properties",
        "data_type": "integer",
        "is_nullable": true,
        "default_value": 0
      }
    },
    {
      "field": "area",
      "type": "float",
      "meta": {
        "interface": "input",
        "display": "formatted-value",
        "display_options": {
          "decimals": 2
        },
        "required": true,
        "sort": 9,
        "width": "half",
        "validation": {
          "_and": [
            {
              "area": {
                "_gt": 0
              }
            }
          ]
        }
      },
      "schema": {
        "name": "area",
        "table": "properties",
        "data_type": "decimal",
        "numeric_precision": 10,
        "numeric_scale": 2,
        "is_nullable": false
      }
    },
    {
      "field": "area_unit",
      "type": "string",
      "meta": {
        "interface": "select-dropdown",
        "display": "labels",
        "display_options": {
          "choices": [
            {"text": "Square Feet", "value": "sqft"},
            {"text": "Square Meters", "value": "sqm"}
          ]
        },
        "sort": 10,
        "width": "half",
        "validation": {
          "_in": ["sqft", "sqm"]
        }
      },
      "schema": {
        "name": "area_unit",
        "table": "properties",
        "data_type": "varchar",
        "max_length": 10,
        "is_nullable": true,
        "default_value": "sqft"
      }
    }
    {
      "field": "address",
      "type": "string",
      "meta": {
        "interface": "input",
        "required": true,
        "sort": 11,
        "width": "full"
      },
      "schema": {
        "name": "address",
        "table": "properties",
        "data_type": "text",
        "is_nullable": false
      }
    },
    {
      "field": "city",
      "type": "string",
      "meta": {
        "interface": "input",
        "required": true,
        "sort": 12,
        "width": "half"
      },
      "schema": {
        "name": "city",
        "table": "properties",
        "data_type": "varchar",
        "max_length": 100,
        "is_nullable": false
      }
    },
    {
      "field": "country",
      "type": "string",
      "meta": {
        "interface": "input",
        "required": true,
        "sort": 13,
        "width": "half"
      },
      "schema": {
        "name": "country",
        "table": "properties",
        "data_type": "varchar",
        "max_length": 100,
        "is_nullable": false
      }
    },
    {
      "field": "coordinates",
      "type": "json",
      "meta": {
        "interface": "input-code",
        "options": {
          "language": "json"
        },
        "sort": 14,
        "width": "half",
        "note": "Latitude and longitude coordinates"
      },
      "schema": {
        "name": "coordinates",
        "table": "properties",
        "data_type": "json",
        "is_nullable": true
      }
    },
    {
      "field": "nearby_places",
      "type": "json",
      "meta": {
        "interface": "input-code",
        "options": {
          "language": "json"
        },
        "sort": 15,
        "width": "half",
        "note": "Generated nearby places data"
      },
      "schema": {
        "name": "nearby_places",
        "table": "properties",
        "data_type": "json",
        "is_nullable": true
      }
    },
    {
      "field": "features",
      "type": "json",
      "meta": {
        "interface": "tags",
        "options": {
          "presets": [
            "swimming-pool", "gym", "garden", "balcony", "terrace",
            "garage", "security", "elevator", "air-conditioning",
            "heating", "fireplace", "walk-in-closet", "storage-room",
            "laundry-room", "maid-room", "wifi", "view", "concierge",
            "shopping", "cafe", "parking"
          ]
        },
        "sort": 16,
        "width": "full",
        "note": "Property features and amenities"
      },
      "schema": {
        "name": "features",
        "table": "properties",
        "data_type": "json",
        "is_nullable": true
      }
    },
    {
      "field": "is_luxury",
      "type": "boolean",
      "meta": {
        "interface": "boolean",
        "sort": 17,
        "width": "half"
      },
      "schema": {
        "name": "is_luxury",
        "table": "properties",
        "data_type": "boolean",
        "is_nullable": true,
        "default_value": false
      }
    },
    {
      "field": "featured",
      "type": "boolean",
      "meta": {
        "interface": "boolean",
        "sort": 18,
        "width": "half"
      },
      "schema": {
        "name": "featured",
        "table": "properties",
        "data_type": "boolean",
        "is_nullable": true,
        "default_value": false
      }
    },
    {
      "field": "views",
      "type": "integer",
      "meta": {
        "interface": "input",
        "readonly": true,
        "sort": 19,
        "width": "half",
        "note": "View count (managed by system)"
      },
      "schema": {
        "name": "views",
        "table": "properties",
        "data_type": "integer",
        "is_nullable": true,
        "default_value": 0
      }
    },
    {
      "field": "slug",
      "type": "string",
      "meta": {
        "interface": "input",
        "options": {
          "slug": true
        },
        "required": true,
        "sort": 20,
        "width": "full",
        "validation": {
          "_regex": "^[a-z0-9-]+$"
        }
      },
      "schema": {
        "name": "slug",
        "table": "properties",
        "data_type": "varchar",
        "max_length": 255,
        "is_nullable": false,
        "is_unique": true
      }
    },
    {
      "field": "owner_id",
      "type": "uuid",
      "meta": {
        "interface": "select-dropdown-m2o",
        "display": "related-values",
        "display_options": {
          "template": "{{first_name}} {{last_name}} ({{email}})"
        },
        "sort": 21,
        "width": "half"
      },
      "schema": {
        "name": "owner_id",
        "table": "properties",
        "data_type": "uuid",
        "is_nullable": true,
        "foreign_key_table": "directus_users",
        "foreign_key_column": "id"
      }
    },
    {
      "field": "agent_id",
      "type": "uuid",
      "meta": {
        "interface": "select-dropdown-m2o",
        "display": "related-values",
        "display_options": {
          "template": "{{first_name}} {{last_name}} ({{email}})"
        },
        "sort": 22,
        "width": "half"
      },
      "schema": {
        "name": "agent_id",
        "table": "properties",
        "data_type": "uuid",
        "is_nullable": true,
        "foreign_key_table": "directus_users",
        "foreign_key_column": "id"
      }
    },
    {
      "field": "project_id",
      "type": "uuid",
      "meta": {
        "interface": "select-dropdown-m2o",
        "display": "related-values",
        "display_options": {
          "template": "{{title}}"
        },
        "sort": 23,
        "width": "half"
      },
      "schema": {
        "name": "project_id",
        "table": "properties",
        "data_type": "uuid",
        "is_nullable": true,
        "foreign_key_table": "projects",
        "foreign_key_column": "id"
      }
    },
    {
      "field": "images",
      "type": "alias",
      "meta": {
        "interface": "files",
        "special": ["files"],
        "sort": 24,
        "width": "full",
        "note": "Property images"
      }
    },
    {
      "field": "status",
      "type": "string",
      "meta": {
        "interface": "select-dropdown",
        "display": "labels",
        "display_options": {
          "choices": [
            {"text": "Draft", "value": "draft"},
            {"text": "Published", "value": "published"},
            {"text": "Archived", "value": "archived"}
          ]
        },
        "sort": 25,
        "width": "half"
      },
      "schema": {
        "name": "status",
        "table": "properties",
        "data_type": "varchar",
        "max_length": 20,
        "is_nullable": true,
        "default_value": "draft"
      }
    },
    {
      "field": "date_created",
      "type": "timestamp",
      "meta": {
        "interface": "datetime",
        "readonly": true,
        "hidden": true,
        "special": ["date-created"]
      },
      "schema": {
        "name": "date_created",
        "table": "properties",
        "data_type": "timestamp",
        "is_nullable": true
      }
    },
    {
      "field": "date_updated",
      "type": "timestamp",
      "meta": {
        "interface": "datetime",
        "readonly": true,
        "hidden": true,
        "special": ["date-updated"]
      },
      "schema": {
        "name": "date_updated",
        "table": "properties",
        "data_type": "timestamp",
        "is_nullable": true
      }
    }
  ]
}
```

### 2.2 Messages Collection Schema

```json
{
  "collection": "messages",
  "meta": {
    "collection": "messages",
    "icon": "message",
    "note": "User-to-user messaging system",
    "display_template": "{{subject}} - {{sender_id.first_name}}",
    "hidden": false,
    "singleton": false,
    "sort": 2
  },
  "fields": [
    {
      "field": "id",
      "type": "uuid",
      "meta": {
        "hidden": true,
        "readonly": true,
        "interface": "input",
        "special": ["uuid"]
      },
      "schema": {
        "is_primary_key": true,
        "is_nullable": false
      }
    },
    {
      "field": "subject",
      "type": "string",
      "meta": {
        "interface": "input",
        "required": true,
        "sort": 1,
        "width": "full"
      },
      "schema": {
        "name": "subject",
        "table": "messages",
        "data_type": "varchar",
        "max_length": 255,
        "is_nullable": false
      }
    },
    {
      "field": "content",
      "type": "text",
      "meta": {
        "interface": "input-rich-text-html",
        "required": true,
        "sort": 2,
        "width": "full"
      },
      "schema": {
        "name": "content",
        "table": "messages",
        "data_type": "text",
        "is_nullable": false
      }
    },
    {
      "field": "sender_id",
      "type": "uuid",
      "meta": {
        "interface": "select-dropdown-m2o",
        "display": "related-values",
        "display_options": {
          "template": "{{first_name}} {{last_name}}"
        },
        "sort": 3,
        "width": "half"
      },
      "schema": {
        "name": "sender_id",
        "table": "messages",
        "data_type": "uuid",
        "is_nullable": true,
        "foreign_key_table": "directus_users",
        "foreign_key_column": "id"
      }
    },
    {
      "field": "recipient_id",
      "type": "uuid",
      "meta": {
        "interface": "select-dropdown-m2o",
        "display": "related-values",
        "display_options": {
          "template": "{{first_name}} {{last_name}}"
        },
        "sort": 4,
        "width": "half"
      },
      "schema": {
        "name": "recipient_id",
        "table": "messages",
        "data_type": "uuid",
        "is_nullable": true,
        "foreign_key_table": "directus_users",
        "foreign_key_column": "id"
      }
    },
    {
      "field": "message_type",
      "type": "string",
      "meta": {
        "interface": "select-dropdown",
        "display": "labels",
        "display_options": {
          "choices": [
            {"text": "General", "value": "general"},
            {"text": "Inquiry", "value": "inquiry"},
            {"text": "Property Inquiry", "value": "property-inquiry"},
            {"text": "Project Inquiry", "value": "project-inquiry"}
          ]
        },
        "sort": 5,
        "width": "half"
      },
      "schema": {
        "name": "message_type",
        "table": "messages",
        "data_type": "varchar",
        "max_length": 50,
        "is_nullable": true,
        "default_value": "general"
      }
    },
    {
      "field": "is_read",
      "type": "boolean",
      "meta": {
        "interface": "boolean",
        "sort": 6,
        "width": "half"
      },
      "schema": {
        "name": "is_read",
        "table": "messages",
        "data_type": "boolean",
        "is_nullable": true,
        "default_value": false
      }
    },
    {
      "field": "read_at",
      "type": "timestamp",
      "meta": {
        "interface": "datetime",
        "sort": 7,
        "width": "half"
      },
      "schema": {
        "name": "read_at",
        "table": "messages",
        "data_type": "timestamp",
        "is_nullable": true
      }
    },
    {
      "field": "property_id",
      "type": "uuid",
      "meta": {
        "interface": "select-dropdown-m2o",
        "display": "related-values",
        "display_options": {
          "template": "{{title}}"
        },
        "sort": 8,
        "width": "half"
      },
      "schema": {
        "name": "property_id",
        "table": "messages",
        "data_type": "uuid",
        "is_nullable": true,
        "foreign_key_table": "properties",
        "foreign_key_column": "id"
      }
    },
    {
      "field": "project_id",
      "type": "uuid",
      "meta": {
        "interface": "select-dropdown-m2o",
        "display": "related-values",
        "display_options": {
          "template": "{{title}}"
        },
        "sort": 9,
        "width": "half"
      },
      "schema": {
        "name": "project_id",
        "table": "messages",
        "data_type": "uuid",
        "is_nullable": true,
        "foreign_key_table": "projects",
        "foreign_key_column": "id"
      }
    },
    {
      "field": "attachments",
      "type": "alias",
      "meta": {
        "interface": "files",
        "special": ["files"],
        "sort": 10,
        "width": "full",
        "note": "Message attachments"
      }
    }
  ]
}
```

### 2.3 Notifications Collection Schema

```json
{
  "collection": "notifications",
  "meta": {
    "collection": "notifications",
    "icon": "notifications",
    "note": "System notifications",
    "display_template": "{{title}} - {{type}}",
    "sort": 3
  },
  "fields": [
    {
      "field": "id",
      "type": "uuid",
      "meta": {
        "hidden": true,
        "readonly": true,
        "interface": "input",
        "special": ["uuid"]
      },
      "schema": {
        "is_primary_key": true,
        "is_nullable": false
      }
    },
    {
      "field": "title",
      "type": "string",
      "meta": {
        "interface": "input",
        "required": true,
        "sort": 1,
        "width": "full",
        "validation": {
          "_and": [
            {
              "title": {
                "_length": {
                  "_lte": 255
                }
              }
            }
          ]
        }
      },
      "schema": {
        "name": "title",
        "table": "notifications",
        "data_type": "varchar",
        "max_length": 255,
        "is_nullable": false
      }
    },
    {
      "field": "message",
      "type": "text",
      "meta": {
        "interface": "input-multiline",
        "required": true,
        "sort": 2,
        "width": "full"
      },
      "schema": {
        "name": "message",
        "table": "notifications",
        "data_type": "text",
        "is_nullable": false
      }
    },
    {
      "field": "type",
      "type": "string",
      "meta": {
        "interface": "select-dropdown",
        "display": "labels",
        "display_options": {
          "choices": [
            {"text": "Info", "value": "info"},
            {"text": "Success", "value": "success"},
            {"text": "Warning", "value": "warning"},
            {"text": "Error", "value": "error"},
            {"text": "Property Inquiry", "value": "property_inquiry"},
            {"text": "Property Approved", "value": "property_approved"},
            {"text": "Property Rejected", "value": "property_rejected"},
            {"text": "Message Received", "value": "message_received"},
            {"text": "System", "value": "system"}
          ]
        },
        "required": true,
        "sort": 3,
        "width": "half"
      },
      "schema": {
        "name": "type",
        "table": "notifications",
        "data_type": "varchar",
        "max_length": 50,
        "is_nullable": false,
        "default_value": "info"
      }
    },
    {
      "field": "priority",
      "type": "string",
      "meta": {
        "interface": "select-dropdown",
        "display": "labels",
        "display_options": {
          "choices": [
            {"text": "Low", "value": "low"},
            {"text": "Normal", "value": "normal"},
            {"text": "High", "value": "high"},
            {"text": "Urgent", "value": "urgent"}
          ]
        },
        "sort": 4,
        "width": "half"
      },
      "schema": {
        "name": "priority",
        "table": "notifications",
        "data_type": "varchar",
        "max_length": 20,
        "is_nullable": true,
        "default_value": "normal"
      }
    },
    {
      "field": "recipient_id",
      "type": "uuid",
      "meta": {
        "interface": "select-dropdown-m2o",
        "display": "related-values",
        "display_options": {
          "template": "{{first_name}} {{last_name}}"
        },
        "required": true,
        "sort": 5,
        "width": "half"
      },
      "schema": {
        "name": "recipient_id",
        "table": "notifications",
        "data_type": "uuid",
        "is_nullable": false,
        "foreign_key_table": "directus_users",
        "foreign_key_column": "id"
      }
    },
    {
      "field": "is_read",
      "type": "boolean",
      "meta": {
        "interface": "boolean",
        "sort": 6,
        "width": "half"
      },
      "schema": {
        "name": "is_read",
        "table": "notifications",
        "data_type": "boolean",
        "is_nullable": true,
        "default_value": false
      }
    },
    {
      "field": "expires_at",
      "type": "timestamp",
      "meta": {
        "interface": "datetime",
        "sort": 7,
        "width": "half",
        "note": "Optional expiration date"
      },
      "schema": {
        "name": "expires_at",
        "table": "notifications",
        "data_type": "timestamp",
        "is_nullable": true
      }
    }
  ]
}
```

### 2.4 Nearby Place Categories Collection Schema

```json
{
  "collection": "nearby_place_categories",
  "meta": {
    "collection": "nearby_place_categories",
    "icon": "place",
    "note": "Google Maps place categories",
    "display_template": "{{display_name}}",
    "sort": 4
  },
  "fields": [
    {
      "field": "id",
      "type": "uuid",
      "meta": {
        "hidden": true,
        "readonly": true,
        "interface": "input",
        "special": ["uuid"]
      },
      "schema": {
        "is_primary_key": true,
        "is_nullable": false
      }
    },
    {
      "field": "name",
      "type": "string",
      "meta": {
        "interface": "input",
        "required": true,
        "sort": 1,
        "width": "half",
        "validation": {
          "_regex": "^[a-z0-9_-]+$"
        },
        "note": "Internal name (lowercase, no spaces)"
      },
      "schema": {
        "name": "name",
        "table": "nearby_place_categories",
        "data_type": "varchar",
        "max_length": 255,
        "is_nullable": false,
        "is_unique": true
      }
    },
    {
      "field": "display_name",
      "type": "string",
      "meta": {
        "interface": "input",
        "required": true,
        "sort": 2,
        "width": "half",
        "note": "User-friendly display name"
      },
      "schema": {
        "name": "display_name",
        "table": "nearby_place_categories",
        "data_type": "varchar",
        "max_length": 255,
        "is_nullable": false
      }
    },
    {
      "field": "description",
      "type": "text",
      "meta": {
        "interface": "input-multiline",
        "sort": 3,
        "width": "full"
      },
      "schema": {
        "name": "description",
        "table": "nearby_place_categories",
        "data_type": "text",
        "is_nullable": true
      }
    },
    {
      "field": "icon",
      "type": "string",
      "meta": {
        "interface": "input",
        "required": true,
        "sort": 4,
        "width": "half",
        "note": "Icon name or URL"
      },
      "schema": {
        "name": "icon",
        "table": "nearby_place_categories",
        "data_type": "varchar",
        "max_length": 255,
        "is_nullable": false
      }
    },
    {
      "field": "google_place_types",
      "type": "json",
      "meta": {
        "interface": "tags",
        "options": {
          "presets": [
            "restaurant", "cafe", "school", "hospital", "pharmacy",
            "bank", "atm", "gas_station", "shopping_mall", "supermarket",
            "gym", "park", "library", "museum", "movie_theater",
            "bus_station", "subway_station", "airport", "taxi_stand"
          ]
        },
        "required": true,
        "sort": 5,
        "width": "full",
        "note": "Google Places API types"
      },
      "schema": {
        "name": "google_place_types",
        "table": "nearby_place_categories",
        "data_type": "json",
        "is_nullable": false
      }
    },
    {
      "field": "enabled",
      "type": "boolean",
      "meta": {
        "interface": "boolean",
        "sort": 6,
        "width": "half"
      },
      "schema": {
        "name": "enabled",
        "table": "nearby_place_categories",
        "data_type": "boolean",
        "is_nullable": true,
        "default_value": true
      }
    },
    {
      "field": "search_radius",
      "type": "integer",
      "meta": {
        "interface": "input",
        "sort": 7,
        "width": "half",
        "validation": {
          "_and": [
            {
              "search_radius": {
                "_gte": 100
              }
            },
            {
              "search_radius": {
                "_lte": 5000
              }
            }
          ]
        },
        "note": "Search radius in meters (100-5000)"
      },
      "schema": {
        "name": "search_radius",
        "table": "nearby_place_categories",
        "data_type": "integer",
        "is_nullable": true,
        "default_value": 1000
      }
    },
    {
      "field": "max_results",
      "type": "integer",
      "meta": {
        "interface": "input",
        "sort": 8,
        "width": "half",
        "validation": {
          "_and": [
            {
              "max_results": {
                "_gte": 1
              }
            },
            {
              "max_results": {
                "_lte": 20
              }
            }
          ]
        },
        "note": "Maximum results to return (1-20)"
      },
      "schema": {
        "name": "max_results",
        "table": "nearby_place_categories",
        "data_type": "integer",
        "is_nullable": true,
        "default_value": 10
      }
    },
    {
      "field": "priority",
      "type": "integer",
      "meta": {
        "interface": "input",
        "sort": 9,
        "width": "half",
        "note": "Display priority (higher = first)"
      },
      "schema": {
        "name": "priority",
        "table": "nearby_place_categories",
        "data_type": "integer",
        "is_nullable": true,
        "default_value": 0
      }
    },
    {
      "field": "color",
      "type": "string",
      "meta": {
        "interface": "input",
        "sort": 10,
        "width": "half",
        "validation": {
          "_regex": "^#[0-9A-Fa-f]{6}$"
        },
        "note": "Hex color code"
      },
      "schema": {
        "name": "color",
        "table": "nearby_place_categories",
        "data_type": "varchar",
        "max_length": 7,
        "is_nullable": true,
        "default_value": "#3B82F6"
      }
    }
  ]
}
```

---

## 3. Field Configuration Templates

### 3.1 String Field Template
```json
{
  "field": "field_name",
  "type": "string",
  "meta": {
    "interface": "input",
    "display": "formatted-value",
    "required": false,
    "readonly": false,
    "hidden": false,
    "sort": 1,
    "width": "full",
    "note": "Field description",
    "validation": {
      "_and": [
        {
          "field_name": {
            "_length": {
              "_lte": 255
            }
          }
        },
        {
          "field_name": {
            "_regex": "^[a-zA-Z0-9\\s-_]+$"
          }
        }
      ]
    },
    "conditions": [],
    "options": {
      "placeholder": "Enter value...",
      "trim": true,
      "masked": false,
      "clear": true
    }
  },
  "schema": {
    "name": "field_name",
    "table": "collection_name",
    "data_type": "varchar",
    "max_length": 255,
    "is_nullable": true,
    "is_unique": false,
    "has_auto_increment": false,
    "default_value": null
  }
}
```

### 3.2 Text Field Template (Rich Text)
```json
{
  "field": "field_name",
  "type": "text",
  "meta": {
    "interface": "input-rich-text-html",
    "display": "formatted-value",
    "required": false,
    "sort": 1,
    "width": "full",
    "note": "Rich text content",
    "options": {
      "toolbar": [
        "bold", "italic", "underline", "strikethrough",
        "subscript", "superscript", "fontselect", "fontsizeselect",
        "forecolor", "backcolor", "link", "unlink", "anchor",
        "alignleft", "aligncenter", "alignright", "alignjustify",
        "numlist", "bullist", "outdent", "indent", "blockquote",
        "undo", "redo", "removeformat", "code"
      ]
    }
  },
  "schema": {
    "name": "field_name",
    "table": "collection_name",
    "data_type": "text",
    "is_nullable": true
  }
}
```

### 3.3 Number Field Template (Integer)
```json
{
  "field": "field_name",
  "type": "integer",
  "meta": {
    "interface": "input",
    "display": "formatted-value",
    "required": false,
    "sort": 1,
    "width": "half",
    "validation": {
      "_and": [
        {
          "field_name": {
            "_gte": 0
          }
        },
        {
          "field_name": {
            "_lte": 999999
          }
        }
      ]
    },
    "options": {
      "placeholder": "0",
      "min": 0,
      "max": 999999,
      "step": 1
    }
  },
  "schema": {
    "name": "field_name",
    "table": "collection_name",
    "data_type": "integer",
    "is_nullable": true,
    "default_value": 0
  }
}
```

### 3.4 Decimal/Float Field Template
```json
{
  "field": "field_name",
  "type": "float",
  "meta": {
    "interface": "input",
    "display": "formatted-value",
    "display_options": {
      "decimals": 2,
      "prefix": "$",
      "suffix": ""
    },
    "required": false,
    "sort": 1,
    "width": "half",
    "validation": {
      "_and": [
        {
          "field_name": {
            "_gte": 0
          }
        }
      ]
    },
    "options": {
      "placeholder": "0.00",
      "min": 0,
      "step": 0.01
    }
  },
  "schema": {
    "name": "field_name",
    "table": "collection_name",
    "data_type": "decimal",
    "numeric_precision": 15,
    "numeric_scale": 2,
    "is_nullable": true,
    "default_value": 0
  }
}
```

### 3.5 Boolean Field Template
```json
{
  "field": "field_name",
  "type": "boolean",
  "meta": {
    "interface": "boolean",
    "display": "boolean",
    "required": false,
    "sort": 1,
    "width": "half",
    "options": {
      "label": "Enable Feature"
    }
  },
  "schema": {
    "name": "field_name",
    "table": "collection_name",
    "data_type": "boolean",
    "is_nullable": true,
    "default_value": false
  }
}
```

### 3.6 Enumeration/Select Field Template
```json
{
  "field": "field_name",
  "type": "string",
  "meta": {
    "interface": "select-dropdown",
    "display": "labels",
    "display_options": {
      "choices": [
        {"text": "Option 1", "value": "option1"},
        {"text": "Option 2", "value": "option2"},
        {"text": "Option 3", "value": "option3"}
      ]
    },
    "required": false,
    "sort": 1,
    "width": "half",
    "validation": {
      "_in": ["option1", "option2", "option3"]
    },
    "options": {
      "placeholder": "Select an option...",
      "allow_other": false
    }
  },
  "schema": {
    "name": "field_name",
    "table": "collection_name",
    "data_type": "varchar",
    "max_length": 50,
    "is_nullable": true,
    "default_value": "option1"
  }
}
```

### 3.7 JSON Field Template
```json
{
  "field": "field_name",
  "type": "json",
  "meta": {
    "interface": "input-code",
    "display": "formatted-json-value",
    "required": false,
    "sort": 1,
    "width": "full",
    "options": {
      "language": "json",
      "template": "{}",
      "lineNumber": true
    }
  },
  "schema": {
    "name": "field_name",
    "table": "collection_name",
    "data_type": "json",
    "is_nullable": true
  }
}
```

### 3.8 Tags/Array JSON Field Template
```json
{
  "field": "field_name",
  "type": "json",
  "meta": {
    "interface": "tags",
    "display": "labels",
    "required": false,
    "sort": 1,
    "width": "full",
    "options": {
      "presets": [
        "tag1", "tag2", "tag3", "tag4", "tag5"
      ],
      "allowCustom": true,
      "placeholder": "Add tags..."
    }
  },
  "schema": {
    "name": "field_name",
    "table": "collection_name",
    "data_type": "json",
    "is_nullable": true
  }
}
```

### 3.9 Date/DateTime Field Template
```json
{
  "field": "field_name",
  "type": "timestamp",
  "meta": {
    "interface": "datetime",
    "display": "datetime",
    "display_options": {
      "relative": true
    },
    "required": false,
    "sort": 1,
    "width": "half",
    "options": {
      "includeSeconds": false,
      "use24": false
    }
  },
  "schema": {
    "name": "field_name",
    "table": "collection_name",
    "data_type": "timestamp",
    "is_nullable": true
  }
}
```

### 3.10 Many-to-One Relationship Template
```json
{
  "field": "related_id",
  "type": "uuid",
  "meta": {
    "interface": "select-dropdown-m2o",
    "display": "related-values",
    "display_options": {
      "template": "{{field_name}}"
    },
    "required": false,
    "sort": 1,
    "width": "half",
    "options": {
      "template": "{{field_name}}",
      "enableCreate": true,
      "enableSelect": true
    }
  },
  "schema": {
    "name": "related_id",
    "table": "collection_name",
    "data_type": "uuid",
    "is_nullable": true,
    "foreign_key_table": "related_collection",
    "foreign_key_column": "id"
  }
}
```

### 3.11 One-to-Many Relationship Template
```json
{
  "field": "related_items",
  "type": "alias",
  "meta": {
    "interface": "list-o2m",
    "display": "related-values",
    "display_options": {
      "template": "{{field_name}}"
    },
    "required": false,
    "sort": 1,
    "width": "full",
    "special": ["o2m"],
    "options": {
      "template": "{{field_name}}",
      "enableCreate": true,
      "enableSelect": true
    }
  }
}
```

### 3.12 File/Media Field Template
```json
{
  "field": "file_field",
  "type": "uuid",
  "meta": {
    "interface": "file-image",
    "display": "image",
    "required": false,
    "sort": 1,
    "width": "half",
    "options": {
      "crop": true,
      "folder": "properties"
    }
  },
  "schema": {
    "name": "file_field",
    "table": "collection_name",
    "data_type": "uuid",
    "is_nullable": true,
    "foreign_key_table": "directus_files",
    "foreign_key_column": "id"
  }
}
```

### 3.13 Multiple Files Field Template
```json
{
  "field": "files_field",
  "type": "alias",
  "meta": {
    "interface": "files",
    "display": "related-values",
    "display_options": {
      "template": "{{filename_download}}"
    },
    "required": false,
    "sort": 1,
    "width": "full",
    "special": ["files"],
    "options": {
      "folder": "properties",
      "enableCreate": true,
      "enableSelect": true
    }
  }
}
```

---

## 4. Automation Scripts

### 4.1 Complete Schema Import Script

```bash
#!/bin/bash
# File: scripts/import-directus-schema.sh

# Set Directus API endpoint and admin token
DIRECTUS_URL="http://localhost:8055"
ADMIN_TOKEN="your-admin-token"

echo "Starting Directus schema import..."

# Function to create collection
create_collection() {
    local collection_file=$1
    echo "Creating collection from $collection_file..."

    curl -X POST "$DIRECTUS_URL/collections" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d @"$collection_file"
}

# Function to create fields for a collection
create_fields() {
    local collection_name=$1
    local fields_file=$2
    echo "Creating fields for $collection_name from $fields_file..."

    curl -X POST "$DIRECTUS_URL/fields/$collection_name" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d @"$fields_file"
}

# Create collections in order (respecting dependencies)
echo "Creating collections..."
create_collection "schema/collections/memberships.json"
create_collection "schema/collections/projects.json"
create_collection "schema/collections/properties.json"
create_collection "schema/collections/messages.json"
create_collection "schema/collections/notifications.json"
create_collection "schema/collections/nearby_place_categories.json"

# Wait for collections to be created
sleep 2

# Create fields for each collection
echo "Creating fields..."
create_fields "memberships" "schema/fields/memberships_fields.json"
create_fields "projects" "schema/fields/projects_fields.json"
create_fields "properties" "schema/fields/properties_fields.json"
create_fields "messages" "schema/fields/messages_fields.json"
create_fields "notifications" "schema/fields/notifications_fields.json"
create_fields "nearby_place_categories" "schema/fields/nearby_place_categories_fields.json"

# Create relationships
echo "Creating relationships..."
curl -X POST "$DIRECTUS_URL/relations" \
    -H "Authorization: Bearer $ADMIN_TOKEN" \
    -H "Content-Type: application/json" \
    -d @"schema/relations/all_relations.json"

echo "Schema import completed!"
```

### 4.2 Node.js Schema Creation Script

```javascript
// File: scripts/create-directus-schema.js
const { createDirectus, rest, authentication, createCollection, createField, createRelation } = require('@directus/sdk');

const directus = createDirectus('http://localhost:8055').with(rest()).with(authentication());

async function createSchema() {
    try {
        // Login with admin credentials
        await directus.login('<EMAIL>', 'admin-password');

        console.log('Creating collections...');

        // Create collections
        const collections = [
            {
                collection: 'memberships',
                meta: {
                    icon: 'card_membership',
                    note: 'User membership plans',
                    display_template: '{{name}}'
                }
            },
            {
                collection: 'projects',
                meta: {
                    icon: 'business',
                    note: 'Real estate projects',
                    display_template: '{{title}}'
                }
            },
            {
                collection: 'properties',
                meta: {
                    icon: 'home',
                    note: 'Real estate properties',
                    display_template: '{{title}} - {{city}}'
                }
            },
            {
                collection: 'messages',
                meta: {
                    icon: 'message',
                    note: 'User messaging system',
                    display_template: '{{subject}}'
                }
            },
            {
                collection: 'notifications',
                meta: {
                    icon: 'notifications',
                    note: 'System notifications',
                    display_template: '{{title}}'
                }
            },
            {
                collection: 'nearby_place_categories',
                meta: {
                    icon: 'place',
                    note: 'Google Maps place categories',
                    display_template: '{{display_name}}'
                }
            }
        ];

        for (const collection of collections) {
            await directus.request(createCollection(collection));
            console.log(`Created collection: ${collection.collection}`);
        }

        console.log('Creating fields...');

        // Create fields for properties collection
        const propertyFields = [
            {
                field: 'title',
                type: 'string',
                meta: {
                    interface: 'input',
                    required: true,
                    sort: 1
                },
                schema: {
                    max_length: 255,
                    is_nullable: false
                }
            },
            {
                field: 'description',
                type: 'text',
                meta: {
                    interface: 'input-rich-text-html',
                    sort: 2
                }
            },
            {
                field: 'price',
                type: 'float',
                meta: {
                    interface: 'input',
                    required: true,
                    sort: 3
                },
                schema: {
                    numeric_precision: 15,
                    numeric_scale: 2,
                    is_nullable: false
                }
            }
            // Add more fields as needed...
        ];

        for (const field of propertyFields) {
            await directus.request(createField('properties', field));
            console.log(`Created field: properties.${field.field}`);
        }

        console.log('Schema creation completed!');

    } catch (error) {
        console.error('Error creating schema:', error);
    }
}

createSchema();
```

### 4.3 Schema Validation Script

```javascript
// File: scripts/validate-directus-schema.js
const { createDirectus, rest, authentication, readCollections, readFields } = require('@directus/sdk');

const directus = createDirectus('http://localhost:8055').with(rest()).with(authentication());

const expectedCollections = [
    'properties', 'projects', 'memberships', 'messages',
    'notifications', 'nearby_place_categories'
];

const expectedFields = {
    properties: [
        'id', 'title', 'description', 'price', 'currency', 'property_type',
        'offer', 'bedrooms', 'bathrooms', 'area', 'area_unit', 'address',
        'city', 'country', 'coordinates', 'nearby_places', 'features',
        'is_luxury', 'featured', 'views', 'slug', 'owner_id', 'agent_id',
        'project_id', 'status', 'date_created', 'date_updated'
    ],
    messages: [
        'id', 'subject', 'content', 'sender_id', 'recipient_id',
        'property_id', 'project_id', 'message_type', 'is_read',
        'read_at', 'date_created', 'date_updated'
    ],
    notifications: [
        'id', 'title', 'message', 'type', 'priority', 'recipient_id',
        'sender_id', 'is_read', 'read_at', 'expires_at',
        'date_created', 'date_updated'
    ],
    nearby_place_categories: [
        'id', 'name', 'display_name', 'description', 'icon',
        'google_place_types', 'enabled', 'search_radius',
        'max_results', 'priority', 'color', 'date_created', 'date_updated'
    ]
};

async function validateSchema() {
    try {
        await directus.login('<EMAIL>', 'admin-password');

        console.log('Validating Directus schema...');

        // Check collections
        const collections = await directus.request(readCollections());
        const collectionNames = collections.map(c => c.collection);

        console.log('\n=== Collection Validation ===');
        for (const expectedCollection of expectedCollections) {
            if (collectionNames.includes(expectedCollection)) {
                console.log(`✅ Collection '${expectedCollection}' exists`);
            } else {
                console.log(`❌ Collection '${expectedCollection}' missing`);
            }
        }

        // Check fields for each collection
        console.log('\n=== Field Validation ===');
        for (const [collectionName, expectedFieldList] of Object.entries(expectedFields)) {
            if (collectionNames.includes(collectionName)) {
                const fields = await directus.request(readFields(collectionName));
                const fieldNames = fields.map(f => f.field);

                console.log(`\nCollection: ${collectionName}`);
                for (const expectedField of expectedFieldList) {
                    if (fieldNames.includes(expectedField)) {
                        console.log(`  ✅ Field '${expectedField}' exists`);
                    } else {
                        console.log(`  ❌ Field '${expectedField}' missing`);
                    }
                }
            }
        }

        console.log('\n=== Validation Complete ===');

    } catch (error) {
        console.error('Error validating schema:', error);
    }
}

validateSchema();
```

---

## 5. Validation and Testing

### 5.1 Schema Validation Checklist

- [ ] All 7 collections created successfully
- [ ] All required fields present with correct types
- [ ] Validation rules applied correctly
- [ ] Relationships configured properly
- [ ] Default values set appropriately
- [ ] Indexes created for performance
- [ ] File upload restrictions configured
- [ ] User permissions set correctly

### 5.2 Field Type Verification Commands

```bash
# Verify collection exists
curl -H "Authorization: Bearer $TOKEN" \
     "$DIRECTUS_URL/collections/properties"

# Verify fields
curl -H "Authorization: Bearer $TOKEN" \
     "$DIRECTUS_URL/fields/properties"

# Test field validation
curl -X POST -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"title": "", "price": -100}' \
     "$DIRECTUS_URL/items/properties"
```

### 5.3 Performance Testing

```javascript
// Test query performance
const start = Date.now();
const properties = await directus.request(
    readItems('properties', {
        filter: { city: { _icontains: 'dubai' } },
        limit: 20
    })
);
const duration = Date.now() - start;
console.log(`Query took ${duration}ms`);
```

This comprehensive reference guide provides everything needed to migrate your Strapi v5 schema to Directus with complete field type mappings, validation rules, and automation scripts.
