'use client';

import React, { useState } from 'react';
import { 
  Bed, Bath, Car, Wifi, Dumbbell, Shield, Heart, 
  Trees, Waves, Mountain, Coffee, ShoppingBag, 
  Check, X, Star, Crown, Home
} from 'lucide-react';

interface AdvancedFiltersProps {
  bedrooms: string;
  bathrooms: string;
  parking: string;
  isLuxury: boolean;
  furnished: boolean;
  petFriendly: boolean;
  features: string[];
  yearBuilt: string;
  onBedroomsChange: (value: string) => void;
  onBathroomsChange: (value: string) => void;
  onParkingChange: (value: string) => void;
  onLuxuryChange: (value: boolean) => void;
  onFurnishedChange: (value: boolean) => void;
  onPetFriendlyChange: (value: boolean) => void;
  onFeaturesChange: (features: string[]) => void;
  onYearBuiltChange: (value: string) => void;
  className?: string;
}

const BEDROOM_OPTIONS = [
  { value: '', label: 'Any Bedrooms' },
  { value: '1', label: '1 Bedroom' },
  { value: '2', label: '2 Bedrooms' },
  { value: '3', label: '3 Bedrooms' },
  { value: '4', label: '4 Bedrooms' },
  { value: '5', label: '5+ Bedrooms' },
];

const BATHROOM_OPTIONS = [
  { value: '', label: 'Any Bathrooms' },
  { value: '1', label: '1 Bathroom' },
  { value: '2', label: '2 Bathrooms' },
  { value: '3', label: '3 Bathrooms' },
  { value: '4', label: '4+ Bathrooms' },
];

const PARKING_OPTIONS = [
  { value: '', label: 'Any Parking' },
  { value: '1', label: '1 Space' },
  { value: '2', label: '2 Spaces' },
  { value: '3', label: '3+ Spaces' },
];

const YEAR_BUILT_OPTIONS = [
  { value: '', label: 'Any Year' },
  { value: '2020', label: '2020 or newer' },
  { value: '2010', label: '2010 or newer' },
  { value: '2000', label: '2000 or newer' },
  { value: '1990', label: '1990 or newer' },
];

const PROPERTY_FEATURES = [
  { value: 'pool', label: 'Swimming Pool', icon: Waves, category: 'amenities' },
  { value: 'gym', label: 'Gym/Fitness', icon: Dumbbell, category: 'amenities' },
  { value: 'security', label: '24/7 Security', icon: Shield, category: 'security' },
  { value: 'garden', label: 'Garden/Balcony', icon: Trees, category: 'outdoor' },
  { value: 'view', label: 'City/Sea View', icon: Mountain, category: 'outdoor' },
  { value: 'wifi', label: 'High-Speed WiFi', icon: Wifi, category: 'technology' },
  { value: 'concierge', label: 'Concierge Service', icon: Heart, category: 'services' },
  { value: 'shopping', label: 'Shopping Center', icon: ShoppingBag, category: 'location' },
  { value: 'cafe', label: 'Nearby Cafes', icon: Coffee, category: 'location' },
];

const FEATURE_CATEGORIES = {
  amenities: 'Amenities',
  security: 'Security',
  outdoor: 'Outdoor',
  technology: 'Technology',
  services: 'Services',
  location: 'Location',
};

export const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  bedrooms,
  bathrooms,
  parking,
  isLuxury,
  furnished,
  petFriendly,
  features,
  yearBuilt,
  onBedroomsChange,
  onBathroomsChange,
  onParkingChange,
  onLuxuryChange,
  onFurnishedChange,
  onPetFriendlyChange,
  onFeaturesChange,
  onYearBuiltChange,
  className = '',
}) => {
  const [showAllFeatures, setShowAllFeatures] = useState(false);

  const handleFeatureToggle = (featureValue: string) => {
    if (features.includes(featureValue)) {
      onFeaturesChange(features.filter(f => f !== featureValue));
    } else {
      onFeaturesChange([...features, featureValue]);
    }
  };

  const clearAllFeatures = () => {
    onFeaturesChange([]);
  };

  const groupedFeatures = PROPERTY_FEATURES.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = [];
    }
    acc[feature.category].push(feature);
    return acc;
  }, {} as Record<string, typeof PROPERTY_FEATURES>);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Room Counts */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Room Requirements</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Bed className="inline w-4 h-4 mr-1" />
              Bedrooms
            </label>
            <select
              value={bedrooms}
              onChange={(e) => onBedroomsChange(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {BEDROOM_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Bath className="inline w-4 h-4 mr-1" />
              Bathrooms
            </label>
            <select
              value={bathrooms}
              onChange={(e) => onBathroomsChange(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {BATHROOM_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Car className="inline w-4 h-4 mr-1" />
              Parking
            </label>
            <select
              value={parking}
              onChange={(e) => onParkingChange(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {PARKING_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Property Characteristics */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Property Characteristics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Year Built
            </label>
            <select
              value={yearBuilt}
              onChange={(e) => onYearBuiltChange(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {YEAR_BUILT_OPTIONS.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Special Features
            </label>
            
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={isLuxury}
                  onChange={(e) => onLuxuryChange(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Crown className="ml-2 mr-2 h-4 w-4 text-yellow-500" />
                <span className="text-sm text-gray-700">Luxury Property</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={furnished}
                  onChange={(e) => onFurnishedChange(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Home className="ml-2 mr-2 h-4 w-4 text-blue-500" />
                <span className="text-sm text-gray-700">Furnished</span>
              </label>

              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={petFriendly}
                  onChange={(e) => onPetFriendlyChange(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Heart className="ml-2 mr-2 h-4 w-4 text-pink-500" />
                <span className="text-sm text-gray-700">Pet Friendly</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Features */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Amenities & Features</h3>
          {features.length > 0 && (
            <button
              type="button"
              onClick={clearAllFeatures}
              className="text-sm text-red-600 hover:text-red-700 flex items-center space-x-1"
            >
              <X className="w-3 h-3" />
              <span>Clear All</span>
            </button>
          )}
        </div>

        <div className="space-y-4">
          {Object.entries(groupedFeatures).map(([category, categoryFeatures]) => (
            <div key={category}>
              <h4 className="text-sm font-medium text-gray-700 mb-2">
                {FEATURE_CATEGORIES[category as keyof typeof FEATURE_CATEGORIES]}
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {categoryFeatures.map((feature) => {
                  const Icon = feature.icon;
                  const isSelected = features.includes(feature.value);
                  
                  return (
                    <button
                      key={feature.value}
                      type="button"
                      onClick={() => handleFeatureToggle(feature.value)}
                      className={`p-3 border rounded-lg text-left transition-all duration-200 ${
                        isSelected
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <Icon className={`w-4 h-4 ${isSelected ? 'text-blue-600' : 'text-gray-500'}`} />
                        <span className="text-sm font-medium">{feature.label}</span>
                        {isSelected && (
                          <Check className="w-3 h-3 text-blue-600 ml-auto" />
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Selected Features Summary */}
        {features.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-blue-800">
                {features.length} feature{features.length !== 1 ? 's' : ''} selected
              </span>
            </div>
            <div className="flex flex-wrap gap-1">
              {features.map(featureValue => {
                const feature = PROPERTY_FEATURES.find(f => f.value === featureValue);
                return (
                  <span
                    key={featureValue}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                  >
                    {feature?.label}
                    <button
                      type="button"
                      onClick={() => handleFeatureToggle(featureValue)}
                      className="ml-1 hover:text-blue-600"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
