# Analytics Migration Guide: Custom Implementation → Chartbrew Plugin

## Overview

This guide provides step-by-step instructions for migrating from our custom analytics implementation to the official Chartbrew plugin for Strapi v5. This migration will improve performance, reduce server load, and provide professional-grade analytics capabilities.

## Prerequisites

- Strapi v5 instance running
- Node.js and npm/yarn installed
- Admin access to Strapi dashboard
- Backup of current database

## Phase 1: Pre-Migration Setup

### 1.1 Create Data Backup

Before making any changes, create a complete backup:

```bash
# Backup database
npm run strapi export

# Create analytics data export
node scripts/export-analytics.js
```

Create the export script `scripts/export-analytics.js`:

```javascript
const fs = require('fs');

async function exportAnalytics() {
  const strapi = require('../backend/src/index.js');
  
  try {
    // Export current analytics data
    const viewTracker = strapi.service('api::property.viewTracker');
    const stats = viewTracker.getStats();
    
    const backup = {
      exportDate: new Date().toISOString(),
      analytics: stats.analytics,
      timestamp: Date.now()
    };
    
    await fs.promises.writeFile(
      'analytics-backup.json', 
      JSON.stringify(backup, null, 2)
    );
    
    console.log('Analytics backup created: analytics-backup.json');
  } catch (error) {
    console.error('Backup failed:', error);
  }
}

exportAnalytics();
```

### 1.2 Install Chartbrew Plugin

```bash
# Install the plugin
npm install @chartbrew/plugin-strapi

# Build Strapi
npm run build
```

### 1.3 Configure Plugin

Create or update `config/plugins.js`:

```javascript
module.exports = {
  chartbrew: {
    enabled: true,
    config: {
      // For localhost development
      apiUrl: process.env.CHARTBREW_API_URL || 'http://localhost:4019',
      frontendUrl: process.env.CHARTBREW_FRONTEND_URL || 'http://localhost:4018',
    }
  }
};
```

## Phase 2: Localhost Chartbrew Setup

### 2.1 Self-Hosted Chartbrew (Recommended for Development)

**Option A: Docker Setup**

```bash
# Clone Chartbrew
git clone https://github.com/chartbrew/chartbrew.git
cd chartbrew

# Start with Docker
docker-compose up -d
```

**Option B: Manual Setup**

```bash
# Clone and setup
git clone https://github.com/chartbrew/chartbrew.git
cd chartbrew

# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your database settings

# Run migrations
npm run db:migrate

# Start development servers
npm run dev
```

### 2.2 Localhost Security Configuration

For localhost development, update `config/middlewares.js`:

```javascript
module.exports = [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          'connect-src': ["'self'", 'http:', 'https:'],
          'frame-src': ["'self'", 'http://localhost:4018'],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  'strapi::cors',
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
```

## Phase 3: Files to Remove

### 3.1 Backend Files - Complete Removal

**Remove these files entirely:**

1. `backend/src/api/property/services/viewTracker.ts`
   - **Action**: Delete entire file
   - **Reason**: Custom analytics service replaced by Chartbrew

### 3.2 Backend Files - Partial Removal

**File**: `backend/src/api/property/controllers/property.ts`

**Remove these methods (approximate line numbers):**

- Lines 825-920: `getAnalytics()` method
- Lines 921-980: `getPropertyAnalytics()` method  
- Lines 981-1020: `getViewStats()` method
- Lines 15-20: Analytics cache imports and variables

**Keep**: All other property controller methods

**File**: `backend/src/api/property/routes/custom.ts`

**Remove these route definitions (lines 117-135):**

```javascript
// Remove these routes:
{
  method: 'GET',
  path: '/properties/analytics',
  handler: 'property.getAnalytics',
},
{
  method: 'GET', 
  path: '/properties/:id/analytics',
  handler: 'property.getPropertyAnalytics',
},
{
  method: 'GET',
  path: '/properties/view-stats', 
  handler: 'property.getViewStats',
},
```

### 3.3 Frontend Files - Complete Removal

**Remove these files entirely:**

1. `frontend/src/app/dashboard/analytics/page.tsx`
2. `frontend/src/components/Analytics/MetricCard.tsx`
3. `frontend/src/components/Analytics/ViewTrendsChart.tsx`
4. `frontend/src/components/Analytics/TopPropertiesCard.tsx`

**Remove the entire Analytics directory:**
```bash
rm -rf frontend/src/components/Analytics/
```

### 3.4 Frontend Files - Partial Removal

**File**: `frontend/src/lib/api.ts`

**Remove these methods (lines 354-384):**

```javascript
// Remove these API methods:
getAnalytics: async () => { ... },
getPropertyAnalytics: async (propertyId: string) => { ... },
getViewStats: async () => { ... },
```

## Phase 4: Code Modification Steps

### 4.1 Preserve Essential View Tracking

**File**: `backend/src/api/property/services/viewTracker.ts`

Instead of deleting, modify to keep only view counting:

```javascript
// Simplified ViewTracker - keep only essential functionality
class ViewTracker {
  private viewQueue: ViewTrackingEntry[] = [];
  private viewCache: ViewCountCache = {};
  private sessionTracker: SessionTracker = {};
  
  // KEEP: Basic view tracking methods
  async trackView(propertyId: string, userAgent?: string, ip?: string) { ... }
  private async processQueue() { ... }
  private async updateViewCount(propertyId: string) { ... }
  
  // REMOVE: All analytics-related methods
  // - trackAnalytics()
  // - getStats()
  // - getDailyTrend()
  // - getTopViewedProperties()
  // - analytics property and related code
}
```

### 4.2 Create Chartbrew Data Endpoint

**File**: `backend/src/api/property/controllers/property.ts`

Add this new method for Chartbrew integration:

```javascript
async getChartbrewData(ctx) {
  try {
    const user = ctx.state.user;
    if (!user) {
      return ctx.unauthorized('Authentication required');
    }

    const properties = await strapi.db.query('api::property.property').findMany({
      where: { owner: user.id },
      select: ['id', 'documentId', 'title', 'views', 'publishedAt', 'createdAt'],
      orderBy: { createdAt: 'desc' }
    });

    const formattedData = properties.map(property => ({
      property_id: property.id,
      title: property.title,
      views: property.views || 0,
      status: property.publishedAt ? 'published' : 'draft',
      created_date: property.createdAt,
      user_id: user.id
    }));

    return { data: formattedData };
  } catch (error) {
    return ctx.internalServerError('Failed to fetch data for Chartbrew');
  }
}
```

### 4.3 Add Chartbrew Route

**File**: `backend/src/api/property/routes/custom.ts`

Add this route:

```javascript
{
  method: 'GET',
  path: '/properties/chartbrew-data',
  handler: 'property.getChartbrewData',
  config: {
    policies: [],
    middlewares: [],
  },
},
```

## Phase 5: Migration Checklist

### Pre-Migration Checklist

- [ ] Database backup completed
- [ ] Analytics data exported to `analytics-backup.json`
- [ ] Chartbrew installed and running on localhost
- [ ] Plugin installed and configured
- [ ] Security middleware updated for localhost

### Migration Execution Checklist

- [ ] Remove frontend Analytics components directory
- [ ] Remove analytics methods from `api.ts`
- [ ] Remove analytics routes from `custom.ts`
- [ ] Remove analytics methods from property controller
- [ ] Modify ViewTracker service (keep view counting only)
- [ ] Add Chartbrew data endpoint
- [ ] Add Chartbrew route
- [ ] Build and restart Strapi: `npm run build && npm run develop`

### Post-Migration Verification

- [ ] Strapi starts without errors
- [ ] Property view counting still works
- [ ] Chartbrew plugin appears in admin sidebar
- [ ] Can connect to Chartbrew from plugin
- [ ] Test data endpoint: `GET /api/properties/chartbrew-data`
- [ ] Create test dashboard in Chartbrew
- [ ] Verify multi-tenant data isolation

## Phase 6: Rollback Plan

If issues arise during migration:

### Immediate Rollback Steps

1. **Restore from Git** (if using version control):
```bash
git checkout HEAD~1  # Go back to previous commit
npm run build
```

2. **Manual Rollback**:
```bash
# Restore backup files
cp backup/viewTracker.ts backend/src/api/property/services/
cp backup/property.ts backend/src/api/property/controllers/
cp backup/custom.ts backend/src/api/property/routes/
cp -r backup/Analytics/ frontend/src/components/

# Rebuild
npm run build
```

3. **Database Restore** (if needed):
```bash
npm run strapi import
```

### Rollback Verification

- [ ] All original analytics endpoints working
- [ ] Frontend analytics dashboard accessible
- [ ] View tracking functioning
- [ ] No console errors

## Phase 7: Localhost Testing Procedures

### 7.1 Basic Functionality Tests

```bash
# Test view tracking
curl http://localhost:1337/api/properties/1/view

# Test Chartbrew data endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:1337/api/properties/chartbrew-data

# Test Chartbrew connection
# Access: http://localhost:1337/admin/plugins/chartbrew
```

### 7.2 Multi-Tenant Testing

1. Create test users with different properties
2. Verify data isolation in Chartbrew endpoint
3. Test dashboard creation for each user
4. Confirm no cross-user data leakage

## Troubleshooting

### Common Issues

**Issue**: Chartbrew plugin not appearing in admin
**Solution**: Check plugin configuration and rebuild Strapi

**Issue**: CORS errors in localhost
**Solution**: Verify middleware configuration includes localhost URLs

**Issue**: Authentication errors
**Solution**: Create proper API token with correct permissions

**Issue**: Data not showing in Chartbrew
**Solution**: Check data endpoint response format and user permissions

## Support Resources

- [Chartbrew Documentation](https://docs.chartbrew.com)
- [Strapi Plugin Development](https://docs.strapi.io/dev-docs/plugins-development)
- [Chartbrew GitHub Issues](https://github.com/chartbrew/chartbrew/issues)

## Migration Complete

After successful migration:
- Custom analytics code removed
- Chartbrew plugin operational
- Historical data preserved
- Performance improved
- Professional analytics dashboard available

**Next Steps**: Configure dashboards, train users, monitor performance improvements.
