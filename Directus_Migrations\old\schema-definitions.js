/**
 * Complete Schema Definitions for Directus Migration
 * Based on DIRECTUS_FIELD_TYPES_REFERENCE.md
 */

const schemaDefinitions = {
  collections: {
    memberships: {
      collection: 'memberships',
      meta: {
        collection: 'memberships',
        icon: 'card_membership',
        note: 'User membership plans and subscription tiers',
        display_template: '{{name}} - {{price}} {{currency}}',
        hidden: false,
        singleton: false,
        sort: 1,
        color: '#10B981'
      },
      schema: {
        name: 'memberships',
        comment: 'User membership plans and subscription tiers'
      }
    },

    projects: {
      collection: 'projects',
      meta: {
        collection: 'projects',
        icon: 'business',
        note: 'Real estate development projects',
        display_template: '{{title}} - {{developer}}',
        hidden: false,
        singleton: false,
        sort: 2,
        color: '#8B5CF6'
      },
      schema: {
        name: 'projects',
        comment: 'Real estate development projects'
      }
    },

    nearby_place_categories: {
      collection: 'nearby_place_categories',
      meta: {
        collection: 'nearby_place_categories',
        icon: 'place',
        note: 'Google Maps place categories configuration',
        display_template: '{{display_name}}',
        hidden: false,
        singleton: false,
        sort: 3,
        color: '#F59E0B'
      },
      schema: {
        name: 'nearby_place_categories',
        comment: 'Google Maps place categories for property nearby places'
      }
    },

    properties: {
      collection: 'properties',
      meta: {
        collection: 'properties',
        icon: 'home',
        note: 'Real estate properties with complete details',
        display_template: '{{title}} - {{city}}, {{country}}',
        hidden: false,
        singleton: false,
        sort: 4,
        color: '#3B82F6'
      },
      schema: {
        name: 'properties',
        comment: 'Main properties table with all property details'
      }
    },

    messages: {
      collection: 'messages',
      meta: {
        collection: 'messages',
        icon: 'message',
        note: 'User-to-user messaging system',
        display_template: '{{subject}} - {{sender_id.first_name}} to {{recipient_id.first_name}}',
        hidden: false,
        singleton: false,
        sort: 5,
        color: '#06B6D4'
      },
      schema: {
        name: 'messages',
        comment: 'User messaging system with attachments support'
      }
    },

    notifications: {
      collection: 'notifications',
      meta: {
        collection: 'notifications',
        icon: 'notifications',
        note: 'System notifications with priority and expiration',
        display_template: '{{title}} - {{type}} ({{priority}})',
        hidden: false,
        singleton: false,
        sort: 6,
        color: '#EF4444'
      },
      schema: {
        name: 'notifications',
        comment: 'System notifications with different types and priorities'
      }
    }
  },

  fields: {
    memberships: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'name',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 1,
          width: 'half',
          note: 'Membership plan name'
        },
        schema: {
          max_length: 255,
          is_nullable: false,
          is_unique: true
        }
      },
      {
        field: 'slug',
        type: 'string',
        meta: {
          interface: 'input',
          options: { slug: true },
          required: true,
          sort: 2,
          width: 'half',
          validation: {
            _regex: '^[a-z0-9-]+$'
          }
        },
        schema: {
          max_length: 255,
          is_nullable: false,
          is_unique: true
        }
      },
      {
        field: 'description',
        type: 'text',
        meta: {
          interface: 'input-multiline',
          sort: 3,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'price',
        type: 'float',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          display_options: {
            decimals: 2,
            prefix: '$'
          },
          required: true,
          sort: 4,
          width: 'half',
          validation: {
            _and: [{ price: { _gte: 0 } }]
          }
        },
        schema: {
          numeric_precision: 10,
          numeric_scale: 2,
          is_nullable: false,
          default_value: 0
        }
      },
      {
        field: 'currency',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          display_options: {
            choices: [
              { text: 'US Dollar', value: 'USD' },
              { text: 'Euro', value: 'EUR' },
              { text: 'British Pound', value: 'GBP' }
            ]
          },
          sort: 5,
          width: 'half',
          validation: { _in: ['USD', 'EUR', 'GBP'] }
        },
        schema: {
          max_length: 3,
          is_nullable: true,
          default_value: 'USD'
        }
      },
      {
        field: 'duration',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          display_options: {
            choices: [
              { text: 'Monthly', value: 'monthly' },
              { text: 'Yearly', value: 'yearly' },
              { text: 'Lifetime', value: 'lifetime' }
            ]
          },
          sort: 6,
          width: 'half',
          validation: { _in: ['monthly', 'yearly', 'lifetime'] }
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'monthly'
        }
      },
      {
        field: 'features',
        type: 'json',
        meta: {
          interface: 'tags',
          options: {
            presets: [
              'unlimited-properties', 'premium-support', 'analytics',
              'featured-listings', 'priority-placement', 'custom-branding'
            ]
          },
          sort: 7,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'max_properties',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 8,
          width: 'half',
          validation: {
            _and: [{ max_properties: { _gte: 1 } }]
          }
        },
        schema: {
          is_nullable: true,
          default_value: 10
        }
      },
      {
        field: 'max_images',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 9,
          width: 'half',
          validation: {
            _and: [{ max_images: { _gte: 1 } }]
          }
        },
        schema: {
          is_nullable: true,
          default_value: 5
        }
      },
      {
        field: 'can_create_projects',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 10,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'can_access_analytics',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 11,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'can_use_premium_filters',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 12,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'priority',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 13,
          width: 'half',
          note: 'Display priority (higher = first)'
        },
        schema: {
          is_nullable: true,
          default_value: 0
        }
      },
      {
        field: 'is_active',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 14,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: true
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          display_options: {
            choices: [
              { text: 'Draft', value: 'draft' },
              { text: 'Published', value: 'published' },
              { text: 'Archived', value: 'archived' }
            ]
          },
          sort: 15,
          width: 'half'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'published'
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      }
    ],

    projects: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'title',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 1,
          width: 'full'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'description',
        type: 'text',
        meta: {
          interface: 'input-rich-text-html',
          sort: 2,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'developer',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 3,
          width: 'half'
        },
        schema: {
          max_length: 255,
          is_nullable: true
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          display_options: {
            choices: [
              { text: 'Planning', value: 'planning' },
              { text: 'Under Construction', value: 'under-construction' },
              { text: 'Completed', value: 'completed' },
              { text: 'Delivered', value: 'delivered' }
            ]
          },
          sort: 4,
          width: 'half'
        },
        schema: {
          max_length: 50,
          is_nullable: true,
          default_value: 'planning'
        }
      },
      {
        field: 'completion_date',
        type: 'date',
        meta: {
          interface: 'datetime',
          sort: 5,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'starting_price',
        type: 'float',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          display_options: {
            decimals: 2,
            prefix: '$'
          },
          sort: 6,
          width: 'half'
        },
        schema: {
          numeric_precision: 15,
          numeric_scale: 2,
          is_nullable: true
        }
      },
      {
        field: 'currency',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          display_options: {
            choices: [
              { text: 'US Dollar', value: 'USD' },
              { text: 'Euro', value: 'EUR' },
              { text: 'British Pound', value: 'GBP' },
              { text: 'UAE Dirham', value: 'AED' },
              { text: 'Saudi Riyal', value: 'SAR' }
            ]
          },
          sort: 7,
          width: 'half'
        },
        schema: {
          max_length: 3,
          is_nullable: true,
          default_value: 'USD'
        }
      },
      {
        field: 'address',
        type: 'text',
        meta: {
          interface: 'input-multiline',
          required: true,
          sort: 8,
          width: 'full'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'city',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 9,
          width: 'half'
        },
        schema: {
          max_length: 100,
          is_nullable: false
        }
      },
      {
        field: 'country',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 10,
          width: 'half'
        },
        schema: {
          max_length: 100,
          is_nullable: false
        }
      },
      {
        field: 'latitude',
        type: 'float',
        meta: {
          interface: 'input',
          sort: 11,
          width: 'half',
          validation: {
            _and: [
              { latitude: { _gte: -90 } },
              { latitude: { _lte: 90 } }
            ]
          }
        },
        schema: {
          numeric_precision: 10,
          numeric_scale: 8,
          is_nullable: true
        }
      },
      {
        field: 'longitude',
        type: 'float',
        meta: {
          interface: 'input',
          sort: 12,
          width: 'half',
          validation: {
            _and: [
              { longitude: { _gte: -180 } },
              { longitude: { _lte: 180 } }
            ]
          }
        },
        schema: {
          numeric_precision: 11,
          numeric_scale: 8,
          is_nullable: true
        }
      },
      {
        field: 'amenities',
        type: 'json',
        meta: {
          interface: 'tags',
          options: {
            presets: [
              'swimming-pool', 'gym', 'spa', 'playground', 'garden',
              'security', 'parking', 'concierge', 'shopping', 'restaurants'
            ]
          },
          sort: 13,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'virtual_tour',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 14,
          width: 'full',
          note: 'Virtual tour URL'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'payment_plan',
        type: 'text',
        meta: {
          interface: 'input-rich-text-html',
          sort: 15,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'featured',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 16,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'slug',
        type: 'string',
        meta: {
          interface: 'input',
          options: { slug: true },
          required: true,
          sort: 17,
          width: 'full',
          validation: {
            _regex: '^[a-z0-9-]+$'
          }
        },
        schema: {
          max_length: 255,
          is_nullable: false,
          is_unique: true
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          display_options: {
            choices: [
              { text: 'Draft', value: 'draft' },
              { text: 'Published', value: 'published' },
              { text: 'Archived', value: 'archived' }
            ]
          },
          sort: 18,
          width: 'half'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'draft'
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      }
    ],

    nearby_place_categories: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'name',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 1,
          width: 'half',
          validation: {
            _regex: '^[a-z0-9_-]+$'
          },
          note: 'Internal name (lowercase, no spaces)'
        },
        schema: {
          max_length: 255,
          is_nullable: false,
          is_unique: true
        }
      },
      {
        field: 'display_name',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 2,
          width: 'half',
          note: 'User-friendly display name'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'description',
        type: 'text',
        meta: {
          interface: 'input-multiline',
          sort: 3,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'icon',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 4,
          width: 'half',
          note: 'Icon name or URL'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'google_place_types',
        type: 'json',
        meta: {
          interface: 'tags',
          options: {
            presets: [
              'restaurant', 'cafe', 'school', 'hospital', 'pharmacy',
              'bank', 'atm', 'gas_station', 'shopping_mall', 'supermarket',
              'gym', 'park', 'library', 'museum', 'movie_theater',
              'bus_station', 'subway_station', 'airport', 'taxi_stand'
            ]
          },
          required: true,
          sort: 5,
          width: 'full',
          note: 'Google Places API types'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'enabled',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 6,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: true
        }
      },
      {
        field: 'search_radius',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 7,
          width: 'half',
          validation: {
            _and: [
              { search_radius: { _gte: 100 } },
              { search_radius: { _lte: 5000 } }
            ]
          },
          note: 'Search radius in meters (100-5000)'
        },
        schema: {
          is_nullable: true,
          default_value: 1000
        }
      },
      {
        field: 'max_results',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 8,
          width: 'half',
          validation: {
            _and: [
              { max_results: { _gte: 1 } },
              { max_results: { _lte: 20 } }
            ]
          },
          note: 'Maximum results to return (1-20)'
        },
        schema: {
          is_nullable: true,
          default_value: 10
        }
      },
      {
        field: 'priority',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 9,
          width: 'half',
          note: 'Display priority (higher = first)'
        },
        schema: {
          is_nullable: true,
          default_value: 0
        }
      },
      {
        field: 'color',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 10,
          width: 'half',
          validation: {
            _regex: '^#[0-9A-Fa-f]{6}$'
          },
          note: 'Hex color code'
        },
        schema: {
          max_length: 7,
          is_nullable: true,
          default_value: '#3B82F6'
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      }
    ],

    properties: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'title',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 1,
          width: 'full'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'description',
        type: 'text',
        meta: {
          interface: 'input-rich-text-html',
          sort: 2,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'price',
        type: 'decimal',
        meta: {
          interface: 'input',
          required: true,
          sort: 3,
          width: 'half',
          validation: {
            _and: [{ price: { _gte: 0 } }]
          }
        },
        schema: {
          numeric_precision: 15,
          numeric_scale: 2,
          is_nullable: false
        }
      },
      {
        field: 'currency',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          sort: 4,
          width: 'half'
        },
        schema: {
          max_length: 3,
          is_nullable: true,
          default_value: 'USD'
        }
      },
      {
        field: 'property_type',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          required: true,
          sort: 5,
          width: 'half'
        },
        schema: {
          max_length: 50,
          is_nullable: false
        }
      },
      {
        field: 'offer',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          required: true,
          sort: 6,
          width: 'half'
        },
        schema: {
          max_length: 20,
          is_nullable: false
        }
      },
      {
        field: 'area',
        type: 'decimal',
        meta: {
          interface: 'input',
          sort: 7,
          width: 'half'
        },
        schema: {
          numeric_precision: 10,
          numeric_scale: 2,
          is_nullable: true
        }
      },
      {
        field: 'bedrooms',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 8,
          width: 'half',
          validation: {
            _and: [
              { bedrooms: { _gte: 0 } },
              { bedrooms: { _lte: 20 } }
            ]
          }
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'bathrooms',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 9,
          width: 'half',
          validation: {
            _and: [
              { bathrooms: { _gte: 0 } },
              { bathrooms: { _lte: 20 } }
            ]
          }
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'address',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 10,
          width: 'full'
        },
        schema: {
          max_length: 500,
          is_nullable: false
        }
      },
      {
        field: 'city',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 11,
          width: 'half'
        },
        schema: {
          max_length: 100,
          is_nullable: false
        }
      },
      {
        field: 'country',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 12,
          width: 'half'
        },
        schema: {
          max_length: 100,
          is_nullable: false
        }
      },
      {
        field: 'latitude',
        type: 'float',
        meta: {
          interface: 'input',
          sort: 13,
          width: 'half'
        },
        schema: {
          numeric_precision: 10,
          numeric_scale: 8,
          is_nullable: true
        }
      },
      {
        field: 'longitude',
        type: 'float',
        meta: {
          interface: 'input',
          sort: 14,
          width: 'half'
        },
        schema: {
          numeric_precision: 11,
          numeric_scale: 8,
          is_nullable: true
        }
      },
      {
        field: 'owner_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 15,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'agent_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 16,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'slug',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 17,
          width: 'full'
        },
        schema: {
          max_length: 255,
          is_nullable: true,
          is_unique: true
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          sort: 18,
          width: 'half'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'published'
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      }
    ],

    messages: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'subject',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 1,
          width: 'full'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'content',
        type: 'text',
        meta: {
          interface: 'input-rich-text-html',
          required: true,
          sort: 2,
          width: 'full'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'sender_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          required: true,
          sort: 3,
          width: 'half'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'recipient_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          required: true,
          sort: 4,
          width: 'half'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'property_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 5,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'project_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 6,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'is_read',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 7,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      }
    ],

    notifications: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'title',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 1,
          width: 'full'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'message',
        type: 'text',
        meta: {
          interface: 'input-multiline',
          required: true,
          sort: 2,
          width: 'full'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'type',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          required: true,
          sort: 3,
          width: 'half'
        },
        schema: {
          max_length: 50,
          is_nullable: false,
          default_value: 'info'
        }
      },
      {
        field: 'priority',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          sort: 4,
          width: 'half'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'normal'
        }
      },
      {
        field: 'recipient_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          required: true,
          sort: 5,
          width: 'half'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'sender_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 6,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'is_read',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 7,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      }
    ]
  },

  relations: [
    {
      collection: 'properties',
      field: 'owner_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'properties',
        many_field: 'owner_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'properties',
        column: 'owner_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'properties',
      field: 'agent_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'properties',
        many_field: 'agent_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'properties',
        column: 'agent_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'messages',
      field: 'sender_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'messages',
        many_field: 'sender_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'messages',
        column: 'sender_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'messages',
      field: 'recipient_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'messages',
        many_field: 'recipient_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'messages',
        column: 'recipient_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'notifications',
      field: 'recipient_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'notifications',
        many_field: 'recipient_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'notifications',
        column: 'recipient_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'notifications',
      field: 'sender_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'notifications',
        many_field: 'sender_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'notifications',
        column: 'sender_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    }
  ]
};

module.exports = { schemaDefinitions };
