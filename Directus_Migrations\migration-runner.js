#!/usr/bin/env node

/**
 * Complete Schema Migration Runner
 * Executes the full migration using schema definitions from DIRECTUS_FIELD_TYPES_REFERENCE.md
 * 
 * Usage: node scripts/migration-runner.js [environment]
 */

const DirectusMigrator = require('./migrate-to-directus');
const { schemaDefinitions } = require('./schema-definitions');

class MigrationRunner {
  constructor(environment = 'development') {
    this.migrator = new DirectusMigrator(environment);
    this.environment = environment;
  }

  async run() {
    try {
      console.log('🚀 Starting complete schema migration...');
      
      // Initialize migrator
      await this.migrator.initialize();
      await this.migrator.confirmMigration();
      await this.migrator.createBackup();

      // Step 1: Extend users collection first
      await this.migrator.extendUsersCollection();

      // Step 2: Create collections in dependency order
      await this.createCollections();

      // Step 3: Create all fields
      await this.createFields();

      // Step 4: Create relationships
      await this.createRelations();

      // Step 5: Create junction tables for M2M relationships (skipped for now due to permissions)
      // await this.createJunctionTables();

      // Step 6: Validate schema
      await this.validateSchema();

      // Step 7: Performance test
      await this.performanceTest();

      console.log('🎉 Migration completed successfully!');
      await this.migrator.saveMigrationLog();

    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      console.log('🔄 Starting rollback...');
      await this.migrator.rollback();
      throw error;
    }
  }

  async createCollections() {
    console.log('📁 Creating collections...');
    
    const collections = [
      schemaDefinitions.collections.memberships,
      schemaDefinitions.collections.projects,
      schemaDefinitions.collections.nearby_place_categories,
      schemaDefinitions.collections.properties,
      schemaDefinitions.collections.messages,
      schemaDefinitions.collections.notifications
    ];

    for (const collection of collections) {
      await this.migrator.createCollection(collection);
      // Small delay to ensure proper creation order
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('✅ All collections created');
  }

  async createFields() {
    console.log('🔧 Creating fields...');
    
    const fieldDefinitions = schemaDefinitions.fields;
    
    // Create fields for each collection
    for (const [collectionName, fields] of Object.entries(fieldDefinitions)) {
      console.log(`  📝 Creating fields for ${collectionName}...`);
      
      for (const field of fields) {
        await this.migrator.createField(collectionName, field);
        // Small delay to prevent rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.log(`  ✅ Fields created for ${collectionName}`);
    }

    console.log('✅ All fields created');
  }

  async createRelations() {
    console.log('🔗 Creating relationships...');
    
    const relations = schemaDefinitions.relations;
    
    for (const relation of relations) {
      await this.migrator.createRelation(relation);
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    console.log('✅ All relationships created');
  }

  async createJunctionTables() {
    console.log('🔀 Creating junction tables...');
    
    // Properties <-> Files junction table
    const propertiesFilesJunction = {
      collection: 'properties_files',
      meta: {
        hidden: true,
        icon: 'import_export'
      },
      schema: {
        name: 'properties_files'
      }
    };

    await this.migrator.createCollection(propertiesFilesJunction);

    // Junction table fields
    const junctionFields = [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
          interface: 'input',
          readonly: true
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
          is_nullable: false
        }
      },
      {
        field: 'properties_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          foreign_key_table: 'properties',
          foreign_key_column: 'id'
        }
      },
      {
        field: 'directus_files_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          foreign_key_table: 'directus_files',
          foreign_key_column: 'id'
        }
      }
    ];

    for (const field of junctionFields) {
      await this.migrator.createField('properties_files', field);
    }

    // Messages <-> Files junction table
    const messagesFilesJunction = {
      collection: 'messages_files',
      meta: {
        hidden: true,
        icon: 'import_export'
      }
    };

    await this.migrator.createCollection(messagesFilesJunction);

    const messageJunctionFields = [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
          interface: 'input',
          readonly: true
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
          is_nullable: false
        }
      },
      {
        field: 'messages_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          foreign_key_table: 'messages',
          foreign_key_column: 'id'
        }
      },
      {
        field: 'directus_files_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          foreign_key_table: 'directus_files',
          foreign_key_column: 'id'
        }
      }
    ];

    for (const field of messageJunctionFields) {
      await this.migrator.createField('messages_files', field);
    }

    console.log('✅ Junction tables created');
  }

  async validateSchema() {
    console.log('🔍 Validating schema...');
    
    const expectedCollections = [
      'properties', 'projects', 'memberships', 'messages',
      'notifications', 'nearby_place_categories'
    ];

    // Check collections exist
    const { readCollections } = require('@directus/sdk');
    const collections = await this.migrator.directus.request(readCollections());

    const existingCollections = collections.map(c => c.collection);
    
    for (const expectedCollection of expectedCollections) {
      if (existingCollections.includes(expectedCollection)) {
        console.log(`  ✅ Collection '${expectedCollection}' exists`);
      } else {
        throw new Error(`Collection '${expectedCollection}' missing`);
      }
    }

    // Validate critical fields
    const criticalFields = {
      properties: ['title', 'price', 'property_type', 'offer', 'slug'],
      messages: ['subject', 'content', 'sender_id', 'recipient_id'],
      notifications: ['title', 'message', 'type', 'recipient_id'],
      nearby_place_categories: ['name', 'display_name', 'google_place_types']
    };

    for (const [collection, fields] of Object.entries(criticalFields)) {
      const { readFields } = require('@directus/sdk');
      const collectionFields = await this.migrator.directus.request(readFields(collection));

      const fieldNames = collectionFields.map(f => f.field);
      
      for (const field of fields) {
        if (fieldNames.includes(field)) {
          console.log(`  ✅ Field '${collection}.${field}' exists`);
        } else {
          throw new Error(`Critical field '${collection}.${field}' missing`);
        }
      }
    }

    console.log('✅ Schema validation passed');
  }

  async performanceTest() {
    console.log('⚡ Running performance tests...');
    
    try {
      // Test basic collection access
      const { readItems } = require('@directus/sdk');
      const start = Date.now();

      await this.migrator.directus.request(readItems('properties', { limit: 1 }));

      const duration = Date.now() - start;
      console.log(`  ✅ Properties collection access: ${duration}ms`);

      // Test relationship query
      const relationStart = Date.now();

      await this.migrator.directus.request(readItems('properties', {
        fields: ['*', { owner_id: ['first_name', 'last_name'] }],
        limit: 1
      }));

      const relationDuration = Date.now() - relationStart;
      console.log(`  ✅ Relationship query: ${relationDuration}ms`);

      if (duration > 1000 || relationDuration > 2000) {
        console.warn('⚠️  Performance may be suboptimal. Consider adding indexes.');
      }

    } catch (error) {
      console.warn(`⚠️  Performance test failed: ${error.message}`);
    }

    console.log('✅ Performance tests completed');
  }
}

// Run migration if called directly
if (require.main === module) {
  const environment = process.argv[2] || 'development';
  
  const runner = new MigrationRunner(environment);
  
  runner.run()
    .then(() => {
      console.log('🎉 Migration completed successfully!');
      console.log('📖 Next steps:');
      console.log('  1. Run data migration script to transfer existing data');
      console.log('  2. Update frontend API integration');
      console.log('  3. Test all functionality thoroughly');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Migration failed:', error.message);
      console.error(error.stack);
      process.exit(1);
    });
}

module.exports = MigrationRunner;
