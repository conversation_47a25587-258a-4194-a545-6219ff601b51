{"name": "backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "console": "strapi console", "deploy": "strapi deploy", "dev": "strapi develop", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi", "upgrade": "npx @strapi/upgrade latest", "upgrade:dry": "npx @strapi/upgrade latest --dry", "clear-cache": "node scripts/clear-cache.js --all", "clear-cache:build": "node scripts/clear-cache.js --build", "clear-cache:uploads": "node scripts/clear-cache.js --uploads"}, "dependencies": {"@chartbrew/plugin-strapi": "^3.0.0", "@strapi/plugin-cloud": "5.16.1", "@strapi/plugin-graphql": "^5.18.0", "@strapi/plugin-users-permissions": "5.16.1", "@strapi/strapi": "5.16.1", "axios": "^1.10.0", "better-sqlite3": "11.3.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************", "installId": "f559e2ec1bea8028f4962fd05f78fb0795168925b86fe4f685a90881261941db"}}