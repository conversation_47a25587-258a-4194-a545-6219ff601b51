module.exports = [
  'strapi::logger',
  'strapi::errors',
  {
    name: 'strapi::security',
    config: {
      // No custom CSP directives — use the defaults
      contentSecurityPolicy: {
        useDefaults: true,
      },
    },
  },
  {
    name: 'strapi::cors',
    config: {
      // Updated CORS settings for Chartbrew integration
      origin: [
        'http://localhost:1337',
        'http://localhost:3000',
        'http://127.0.0.1:1337',
        'http://localhost:4018',
        'http://localhost:4019',
        'http://127.0.0.1:4018',   // Explicit localhost IP
        'http://127.0.0.1:4019',
        'http://**********:4018',  // Chartbrew container IP
        'http://**********:4019',
        'http://host.docker.internal:4018',
        'http://host.docker.internal:4019',
        'http://***********:4018',  // Host IP for Docker containers
        'http://***********:4019',
        '*'  // Keep wildcard for development
      ],
      headers: ['*'],
      credentials: true,
      methods: ['GET','POST','PUT','PATCH','DELETE','HEAD','OPTIONS'],
    },
  },
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',
];
