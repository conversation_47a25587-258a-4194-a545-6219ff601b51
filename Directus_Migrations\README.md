# Directus Migration Scripts

Comprehensive automated migration scripts for migrating the real estate platform from Strapi v5 to Directus CMS.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Directus instance running
- `jq` and `curl` (for bash scripts)

### Installation

```bash
cd scripts
npm install
```

### Environment Setup

Create environment variables for your Directus instances:

```bash
# Development (default)
# Uses hardcoded values in scripts

# Staging
export DIRECTUS_STAGING_URL="https://staging-directus.yourdomain.com"
export DIRECTUS_STAGING_EMAIL="<EMAIL>"
export DIRECTUS_STAGING_PASSWORD="your-password"
# OR
export DIRECTUS_STAGING_TOKEN="your-static-token"

# Production
export DIRECTUS_PROD_URL="https://directus.yourdomain.com"
export DIRECTUS_PROD_EMAIL="<EMAIL>"
export DIRECTUS_PROD_PASSWORD="your-password"
# OR
export DIRECTUS_PROD_TOKEN="your-static-token"
```

## 📋 Migration Process

### 1. Complete Migration (Recommended)

```bash
# Development
npm run full-migration:dev

# Staging
npm run full-migration:staging

# Production (requires confirmation)
npm run full-migration:prod
```

### 2. Step-by-Step Migration

```bash
# Step 1: Run schema migration
npm run migrate:dev

# Step 2: Validate migration
npm run validate:dev
```

### 3. Bash Script Alternative

```bash
# Make executable
chmod +x migrate-to-directus.sh

# Run migration
./migrate-to-directus.sh development
```

## 🔧 Script Details

### Migration Scripts

| Script | Purpose | Environment Support |
|--------|---------|-------------------|
| `migration-runner.js` | Complete schema migration | ✅ All |
| `migrate-to-directus.js` | Core migration class | ✅ All |
| `migrate-to-directus.sh` | Bash version | ✅ All |
| `schema-definitions.js` | Schema definitions | N/A |

### Validation Scripts

| Script | Purpose | Features |
|--------|---------|----------|
| `validate-migration.js` | Complete validation | Collections, Fields, Relations, Performance, Business Logic |

## 📊 What Gets Migrated

### Collections Created

1. **Properties** - Main property listings with 25+ fields
2. **Projects** - Real estate development projects  
3. **Memberships** - User subscription tiers
4. **Messages** - User-to-user messaging system
5. **Notifications** - System notifications with priorities
6. **Nearby Place Categories** - Google Maps integration
7. **User Extensions** - Extended directus_users fields
8. **Junction Tables** - File relationships

### Field Types Migrated

- ✅ String fields with validation
- ✅ Rich text content
- ✅ Numbers (integer, decimal) with constraints
- ✅ Boolean fields with defaults
- ✅ Date/DateTime fields
- ✅ JSON fields for complex data
- ✅ Enumeration/Select fields
- ✅ File/Media relationships
- ✅ Many-to-One relationships
- ✅ One-to-Many relationships

### Business Logic Preserved

- ✅ Property price validation (≥ 0)
- ✅ Bedroom/bathroom limits (0-20)
- ✅ Search radius constraints (100-5000m)
- ✅ Unique slug constraints
- ✅ Google Maps place type validation
- ✅ Hex color validation
- ✅ Membership feature restrictions

## 🔍 Validation Features

### Collection Validation
- Verifies all 8 collections exist
- Checks collection metadata and configuration

### Field Validation  
- Validates 100+ critical fields across all collections
- Checks field types, constraints, and validation rules
- Verifies relationship field configurations

### Relationship Validation
- Tests 10+ foreign key relationships
- Validates junction table configurations
- Checks cascade delete/update rules

### Performance Testing
- Basic collection access (< 1000ms)
- Relationship queries (< 2000ms)  
- Complex filter queries (< 1500ms)

### Business Logic Testing
- Price validation constraints
- Unique field constraints
- Required field validation
- Data type validation

## 📁 File Structure

```
scripts/
├── migrate-to-directus.js      # Core migration class
├── migration-runner.js         # Complete migration runner
├── migrate-to-directus.sh      # Bash migration script
├── schema-definitions.js       # All schema definitions
├── validate-migration.js       # Validation script
├── package.json               # Dependencies
└── README.md                  # This file

Generated during migration:
├── logs/                      # Migration and validation logs
├── backups/                   # Schema backups
└── reports/                   # Validation reports
```

## 🛡️ Safety Features

### Backup System
- Automatic schema backup before migration
- Timestamped backup files
- Environment-specific backups

### Rollback Capability
- Automatic rollback on migration failure
- Manual rollback support
- Tracks all created resources

### Error Handling
- Comprehensive error logging
- Graceful failure handling
- Detailed error messages

### Production Safety
- Confirmation prompts for production
- Environment validation
- Connection verification

## 📈 Performance Considerations

### Migration Performance
- Batch field creation with delays
- Rate limiting prevention
- Progress tracking

### Query Optimization
- Proper indexing on foreign keys
- Optimized relationship queries
- Efficient validation queries

## 🔧 Troubleshooting

### Common Issues

**Authentication Failed**
```bash
# Check environment variables
echo $DIRECTUS_PROD_TOKEN

# Verify Directus URL
curl -I https://directus.yourdomain.com
```

**Collection Already Exists**
- Scripts automatically skip existing collections
- Check logs for detailed information

**Field Creation Failed**
- Verify field type compatibility
- Check validation rule syntax
- Review Directus version compatibility

**Performance Issues**
- Add database indexes manually
- Optimize Directus configuration
- Check server resources

### Debug Mode

```bash
# Enable verbose logging
DEBUG=true npm run migrate:dev

# Check specific collection
node -e "
const validator = require('./validate-migration.js');
const v = new validator('development');
v.initialize().then(() => v.validateCollections());
"
```

## 📞 Support

### Log Files
- Migration logs: `logs/migration-*.log`
- Validation reports: `logs/validation-report-*.json`
- Backup files: `backups/directus-backup-*.json`

### Manual Verification

```bash
# Check collections via API
curl -H "Authorization: Bearer $TOKEN" \
     "$DIRECTUS_URL/collections"

# Check specific collection fields
curl -H "Authorization: Bearer $TOKEN" \
     "$DIRECTUS_URL/fields/properties"
```

## 🎯 Next Steps After Migration

1. **Run Data Migration**
   ```bash
   # Use the data migration scripts from main migration guide
   node scripts/migrate-data.js
   ```

2. **Update Frontend Integration**
   - Update API calls to use Directus SDK
   - Modify authentication flow
   - Test all functionality

3. **Performance Optimization**
   - Add database indexes
   - Configure caching
   - Optimize queries

4. **Production Deployment**
   - Update environment variables
   - Configure SSL certificates
   - Set up monitoring

## 📄 License

MIT License - see LICENSE file for details.
