#!/bin/bash

# Comprehensive Directus Schema Migration Script (Bash Version)
# Migrates complete real estate platform schema from Strapi v5 to Directus
#
# Usage: ./scripts/migrate-to-directus.sh [environment]
# Example: ./scripts/migrate-to-directus.sh production

set -e  # Exit on any error

# Configuration
ENVIRONMENT=${1:-development}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_DIR="$SCRIPT_DIR/../logs"
BACKUP_DIR="$SCRIPT_DIR/../backups"

# Environment configurations
case $ENVIRONMENT in
  development)
    DIRECTUS_URL="http://localhost:8055"
    DIRECTUS_EMAIL="<EMAIL>"
    DIRECTUS_PASSWORD="Mb123321"
    DIRECTUS_TOKEN="QV5pYPw2G-w0UwBPbgbA7_CgM0MzoYaO"
    ;;
  staging)
    DIRECTUS_URL="${DIRECTUS_STAGING_URL:-https://staging-directus.yourdomain.com}"
    DIRECTUS_EMAIL="$DIRECTUS_STAGING_EMAIL"
    DIRECTUS_PASSWORD="$DIRECTUS_STAGING_PASSWORD"
    DIRECTUS_TOKEN="$DIRECTUS_STAGING_TOKEN"
    ;;
  production)
    DIRECTUS_URL="${DIRECTUS_PROD_URL:-https://directus.yourdomain.com}"
    DIRECTUS_EMAIL="$DIRECTUS_PROD_EMAIL"
    DIRECTUS_PASSWORD="$DIRECTUS_PROD_PASSWORD"
    DIRECTUS_TOKEN="$DIRECTUS_PROD_TOKEN"
    ;;
  *)
    echo "❌ Invalid environment: $ENVIRONMENT. Use: development, staging, or production"
    exit 1
    ;;
esac

# Create directories
mkdir -p "$LOG_DIR" "$BACKUP_DIR"

# Log file
LOG_FILE="$LOG_DIR/migration-bash-$ENVIRONMENT-$(date +%s).log"
touch "$LOG_FILE"

# Logging function
log() {
  local message="$1"
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  echo "[$timestamp] $message" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
  log "❌ ERROR: $1"
  exit 1
}

# Authentication function
authenticate() {
  log "🔐 Authenticating with Directus..."
  
  if [[ -n "$DIRECTUS_TOKEN" ]]; then
    AUTH_HEADER="Authorization: Bearer $DIRECTUS_TOKEN"
    log "✅ Using static token authentication"
  elif [[ -n "$DIRECTUS_EMAIL" && -n "$DIRECTUS_PASSWORD" ]]; then
    # Login and get token
    local login_response=$(curl -s -X POST "$DIRECTUS_URL/auth/login" \
      -H "Content-Type: application/json" \
      -d "{\"email\":\"$DIRECTUS_EMAIL\",\"password\":\"$DIRECTUS_PASSWORD\"}")
    
    local access_token=$(echo "$login_response" | jq -r '.data.access_token // empty')
    
    if [[ -z "$access_token" || "$access_token" == "null" ]]; then
      error_exit "Failed to authenticate with email/password"
    fi
    
    AUTH_HEADER="Authorization: Bearer $access_token"
    log "✅ Authenticated with email/password"
  else
    error_exit "No authentication method configured"
  fi
}

# Verify connection
verify_connection() {
  log "🔍 Verifying connection to Directus..."
  
  local response=$(curl -s -w "%{http_code}" -o /tmp/directus_test \
    -H "$AUTH_HEADER" \
    "$DIRECTUS_URL/collections")
  
  local http_code="${response: -3}"
  
  if [[ "$http_code" != "200" ]]; then
    error_exit "Failed to connect to Directus (HTTP $http_code)"
  fi
  
  local collection_count=$(jq '.data | length' /tmp/directus_test)
  log "✅ Connected to Directus ($collection_count existing collections)"
  rm -f /tmp/directus_test
}

# Confirmation for production
confirm_migration() {
  if [[ "$ENVIRONMENT" == "production" ]]; then
    echo "⚠️  You are about to migrate to PRODUCTION environment."
    echo "This will create collections and fields in your production Directus instance."
    read -p "Are you sure you want to continue? (yes/no): " -r
    
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
      log "❌ Migration cancelled by user"
      exit 0
    fi
  fi
}

# Create backup
create_backup() {
  log "📦 Creating schema backup..."
  
  local backup_file="$BACKUP_DIR/directus-backup-$ENVIRONMENT-$(date +%s).json"
  
  # Get current schema
  curl -s -H "$AUTH_HEADER" "$DIRECTUS_URL/collections" > /tmp/collections.json
  curl -s -H "$AUTH_HEADER" "$DIRECTUS_URL/fields" > /tmp/fields.json
  curl -s -H "$AUTH_HEADER" "$DIRECTUS_URL/relations" > /tmp/relations.json
  
  # Create backup JSON
  jq -n \
    --arg timestamp "$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
    --arg environment "$ENVIRONMENT" \
    --slurpfile collections /tmp/collections.json \
    --slurpfile fields /tmp/fields.json \
    --slurpfile relations /tmp/relations.json \
    '{
      timestamp: $timestamp,
      environment: $environment,
      collections: $collections[0].data,
      fields: $fields[0].data,
      relations: $relations[0].data
    }' > "$backup_file"
  
  log "✅ Backup created: $backup_file"
  
  # Cleanup temp files
  rm -f /tmp/collections.json /tmp/fields.json /tmp/relations.json
}

# Create collection
create_collection() {
  local collection_name="$1"
  local collection_data="$2"
  
  log "📁 Creating collection: $collection_name"
  
  local response=$(curl -s -w "%{http_code}" -o /tmp/create_collection_response \
    -X POST "$DIRECTUS_URL/collections" \
    -H "$AUTH_HEADER" \
    -H "Content-Type: application/json" \
    -d "$collection_data")
  
  local http_code="${response: -3}"
  
  if [[ "$http_code" == "200" || "$http_code" == "201" ]]; then
    log "✅ Collection $collection_name created successfully"
  elif [[ "$http_code" == "400" ]]; then
    local error_message=$(jq -r '.errors[0].message // "Unknown error"' /tmp/create_collection_response)
    if [[ "$error_message" == *"already exists"* ]]; then
      log "⚠️  Collection $collection_name already exists, skipping..."
    else
      error_exit "Failed to create collection $collection_name: $error_message"
    fi
  else
    local error_message=$(jq -r '.errors[0].message // "Unknown error"' /tmp/create_collection_response)
    error_exit "Failed to create collection $collection_name (HTTP $http_code): $error_message"
  fi
  
  rm -f /tmp/create_collection_response
}

# Create field
create_field() {
  local collection_name="$1"
  local field_data="$2"
  local field_name=$(echo "$field_data" | jq -r '.field')
  
  log "🔧 Creating field: $collection_name.$field_name"
  
  local response=$(curl -s -w "%{http_code}" -o /tmp/create_field_response \
    -X POST "$DIRECTUS_URL/fields/$collection_name" \
    -H "$AUTH_HEADER" \
    -H "Content-Type: application/json" \
    -d "$field_data")
  
  local http_code="${response: -3}"
  
  if [[ "$http_code" == "200" || "$http_code" == "201" ]]; then
    log "✅ Field $collection_name.$field_name created successfully"
  elif [[ "$http_code" == "400" ]]; then
    local error_message=$(jq -r '.errors[0].message // "Unknown error"' /tmp/create_field_response)
    if [[ "$error_message" == *"already exists"* ]]; then
      log "⚠️  Field $collection_name.$field_name already exists, skipping..."
    else
      error_exit "Failed to create field $collection_name.$field_name: $error_message"
    fi
  else
    local error_message=$(jq -r '.errors[0].message // "Unknown error"' /tmp/create_field_response)
    error_exit "Failed to create field $collection_name.$field_name (HTTP $http_code): $error_message"
  fi
  
  rm -f /tmp/create_field_response
  
  # Small delay to prevent rate limiting
  sleep 0.1
}

# Create relation
create_relation() {
  local relation_data="$1"
  local relation_name=$(echo "$relation_data" | jq -r '.collection + "." + .field')
  
  log "🔗 Creating relation: $relation_name"
  
  local response=$(curl -s -w "%{http_code}" -o /tmp/create_relation_response \
    -X POST "$DIRECTUS_URL/relations" \
    -H "$AUTH_HEADER" \
    -H "Content-Type: application/json" \
    -d "$relation_data")
  
  local http_code="${response: -3}"
  
  if [[ "$http_code" == "200" || "$http_code" == "201" ]]; then
    log "✅ Relation $relation_name created successfully"
  elif [[ "$http_code" == "400" ]]; then
    local error_message=$(jq -r '.errors[0].message // "Unknown error"' /tmp/create_relation_response)
    if [[ "$error_message" == *"already exists"* ]]; then
      log "⚠️  Relation $relation_name already exists, skipping..."
    else
      error_exit "Failed to create relation $relation_name: $error_message"
    fi
  else
    local error_message=$(jq -r '.errors[0].message // "Unknown error"' /tmp/create_relation_response)
    error_exit "Failed to create relation $relation_name (HTTP $http_code): $error_message"
  fi
  
  rm -f /tmp/create_relation_response
  sleep 0.2
}

# Extend users collection
extend_users_collection() {
  log "👤 Extending directus_users collection..."
  
  # User extension fields
  local user_fields='[
    {
      "field": "first_name",
      "type": "string",
      "meta": {
        "interface": "input",
        "sort": 100,
        "width": "half"
      },
      "schema": {
        "max_length": 255,
        "is_nullable": true
      }
    },
    {
      "field": "last_name",
      "type": "string",
      "meta": {
        "interface": "input",
        "sort": 101,
        "width": "half"
      },
      "schema": {
        "max_length": 255,
        "is_nullable": true
      }
    },
    {
      "field": "phone",
      "type": "string",
      "meta": {
        "interface": "input",
        "sort": 102,
        "width": "half"
      },
      "schema": {
        "max_length": 50,
        "is_nullable": true
      }
    },
    {
      "field": "company",
      "type": "string",
      "meta": {
        "interface": "input",
        "sort": 103,
        "width": "half"
      },
      "schema": {
        "max_length": 255,
        "is_nullable": true
      }
    },
    {
      "field": "job_title",
      "type": "string",
      "meta": {
        "interface": "input",
        "sort": 104,
        "width": "half"
      },
      "schema": {
        "max_length": 255,
        "is_nullable": true
      }
    },
    {
      "field": "bio",
      "type": "text",
      "meta": {
        "interface": "input-multiline",
        "sort": 105,
        "width": "full"
      },
      "schema": {
        "is_nullable": true
      }
    },
    {
      "field": "is_agent",
      "type": "boolean",
      "meta": {
        "interface": "boolean",
        "sort": 106,
        "width": "half"
      },
      "schema": {
        "is_nullable": true,
        "default_value": false
      }
    },
    {
      "field": "membership_id",
      "type": "uuid",
      "meta": {
        "interface": "select-dropdown-m2o",
        "display": "related-values",
        "display_options": {
          "template": "{{name}}"
        },
        "sort": 107,
        "width": "half"
      },
      "schema": {
        "is_nullable": true,
        "foreign_key_table": "memberships",
        "foreign_key_column": "id"
      }
    },
    {
      "field": "membership_expires_at",
      "type": "timestamp",
      "meta": {
        "interface": "datetime",
        "sort": 108,
        "width": "half"
      },
      "schema": {
        "is_nullable": true
      }
    }
  ]'
  
  # Create each field
  echo "$user_fields" | jq -c '.[]' | while read -r field; do
    create_field "directus_users" "$field"
  done
  
  log "✅ User collection extended"
}

# Main migration function
main() {
  log "🚀 Starting Directus schema migration for $ENVIRONMENT environment"
  
  # Check dependencies
  command -v curl >/dev/null 2>&1 || error_exit "curl is required but not installed"
  command -v jq >/dev/null 2>&1 || error_exit "jq is required but not installed"
  
  # Initialize
  authenticate
  verify_connection
  confirm_migration
  create_backup
  
  # Extend users collection first
  extend_users_collection
  
  log "🎯 Basic setup completed. Run the Node.js migration runner for complete schema creation:"
  log "   node scripts/migration-runner.js $ENVIRONMENT"
  log "📋 Migration log: $LOG_FILE"
}

# Run main function
main "$@"
