{"name": "directus-migration-scripts", "version": "1.0.0", "description": "Automated migration scripts for Strapi v5 to Directus CMS", "main": "migration-runner.js", "scripts": {"migrate": "node migration-runner.js", "migrate:dev": "node migration-runner.js development", "migrate:staging": "node migration-runner.js staging", "migrate:prod": "node migration-runner.js production", "validate": "node validate-migration.js", "validate:dev": "node validate-migration.js development", "validate:staging": "node validate-migration.js staging", "validate:prod": "node validate-migration.js production", "migrate:bash": "./migrate-to-directus.sh", "migrate:bash:dev": "./migrate-to-directus.sh development", "migrate:bash:staging": "./migrate-to-directus.sh staging", "migrate:bash:prod": "./migrate-to-directus.sh production", "full-migration": "npm run migrate && npm run validate", "full-migration:dev": "npm run migrate:dev && npm run validate:dev", "full-migration:staging": "npm run migrate:staging && npm run validate:staging", "full-migration:prod": "npm run migrate:prod && npm run validate:prod"}, "dependencies": {"@directus/sdk": "^17.0.0"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["directus", "strapi", "migration", "cms", "real-estate", "schema"], "author": "Real Estate Platform Team", "license": "MIT"}