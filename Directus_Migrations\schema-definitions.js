/**
 * Enhanced Schema Definitions for Directus Migration
 * Fixed field name conflicts, added missing choices/options, and proper file field configurations
 * Based on Strapi 5 schema analysis and Directus best practices
 */

const schemaDefinitions = {
  collections: {
    memberships: {
      collection: 'memberships',
      meta: {
        collection: 'memberships',
        icon: 'card_membership',
        note: 'User membership plans and subscription tiers',
        display_template: '{{name}} - {{price}} {{currency}}',
        hidden: false,
        singleton: false,
        sort: 1,
        color: '#10B981'
      },
      schema: {
        name: 'memberships',
        comment: 'User membership plans and subscription tiers'
      }
    },

    projects: {
      collection: 'projects',
      meta: {
        collection: 'projects',
        icon: 'business',
        note: 'Real estate development projects',
        display_template: '{{title}} - {{developer}}',
        hidden: false,
        singleton: false,
        sort: 2,
        color: '#8B5CF6'
      },
      schema: {
        name: 'projects',
        comment: 'Real estate development projects'
      }
    },

    nearby_place_categories: {
      collection: 'nearby_place_categories',
      meta: {
        collection: 'nearby_place_categories',
        icon: 'place',
        note: 'Google Maps place categories configuration',
        display_template: '{{display_name}}',
        hidden: false,
        singleton: false,
        sort: 3,
        color: '#F59E0B'
      },
      schema: {
        name: 'nearby_place_categories',
        comment: 'Google Maps place categories for property nearby places'
      }
    },

    properties: {
      collection: 'properties',
      meta: {
        collection: 'properties',
        icon: 'home',
        note: 'Real estate properties with complete details',
        display_template: '{{title}} - {{city}}, {{country}}',
        hidden: false,
        singleton: false,
        sort: 4,
        color: '#3B82F6'
      },
      schema: {
        name: 'properties',
        comment: 'Main properties table with all property details'
      }
    },

    messages: {
      collection: 'messages',
      meta: {
        collection: 'messages',
        icon: 'message',
        note: 'User-to-user messaging system',
        display_template: '{{subject}} - {{sender_id.first_name}} to {{recipient_id.first_name}}',
        hidden: false,
        singleton: false,
        sort: 5,
        color: '#06B6D4'
      },
      schema: {
        name: 'messages',
        comment: 'User messaging system with attachments support'
      }
    },

    notifications: {
      collection: 'notifications',
      meta: {
        collection: 'notifications',
        icon: 'notifications',
        note: 'System notifications with priority and expiration',
        display_template: '{{title}} - {{type}} ({{priority}})',
        hidden: false,
        singleton: false,
        sort: 6,
        color: '#EF4444'
      },
      schema: {
        name: 'notifications',
        comment: 'System notifications with different types and priorities'
      }
    },

    // Junction tables for file relationships
    properties_files: {
      collection: 'properties_files',
      meta: {
        collection: 'properties_files',
        icon: 'import_export',
        note: 'Junction table for property images and files',
        hidden: true,
        singleton: false,
        sort: 7
      },
      schema: {
        name: 'properties_files',
        comment: 'Junction table linking properties to files'
      }
    },

    projects_files: {
      collection: 'projects_files',
      meta: {
        collection: 'projects_files',
        icon: 'import_export',
        note: 'Junction table for project images and files',
        hidden: true,
        singleton: false,
        sort: 8
      },
      schema: {
        name: 'projects_files',
        comment: 'Junction table linking projects to files'
      }
    },

    messages_files: {
      collection: 'messages_files',
      meta: {
        collection: 'messages_files',
        icon: 'import_export',
        note: 'Junction table for message attachments',
        hidden: true,
        singleton: false,
        sort: 9
      },
      schema: {
        name: 'messages_files',
        comment: 'Junction table linking messages to file attachments'
      }
    }
  },

  fields: {
    // Memberships fields with proper choices and Directus core fields
    memberships: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Draft', value: 'draft' },
              { text: 'Published', value: 'published' },
              { text: 'Archived', value: 'archived' }
            ]
          },
          sort: 1,
          width: 'half',
          note: 'Directus workflow status'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'draft'
        }
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
          special: ['sort']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_created',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_updated',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'name',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 10,
          width: 'half',
          note: 'Membership plan name'
        },
        schema: {
          max_length: 255,
          is_nullable: false,
          is_unique: true
        }
      },
      {
        field: 'slug',
        type: 'string',
        meta: {
          interface: 'input',
          options: { slug: true },
          required: true,
          sort: 11,
          width: 'half',
          validation: {
            _regex: '^[a-z0-9-]+$'
          }
        },
        schema: {
          max_length: 255,
          is_nullable: false,
          is_unique: true
        }
      },
      {
        field: 'description',
        type: 'text',
        meta: {
          interface: 'input-multiline',
          sort: 12,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'price',
        type: 'float',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          display_options: {
            decimals: 2,
            prefix: '$'
          },
          required: true,
          sort: 13,
          width: 'half',
          validation: {
            _and: [{ price: { _gte: 0 } }]
          }
        },
        schema: {
          numeric_precision: 10,
          numeric_scale: 2,
          is_nullable: false,
          default_value: 0
        }
      },
      {
        field: 'currency',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'US Dollar', value: 'USD' },
              { text: 'Euro', value: 'EUR' },
              { text: 'British Pound', value: 'GBP' },
              { text: 'Canadian Dollar', value: 'CAD' },
              { text: 'Australian Dollar', value: 'AUD' }
            ]
          },
          sort: 14,
          width: 'half',
          validation: { _in: ['USD', 'EUR', 'GBP', 'CAD', 'AUD'] }
        },
        schema: {
          max_length: 3,
          is_nullable: true,
          default_value: 'USD'
        }
      },
      {
        field: 'duration',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Monthly', value: 'monthly' },
              { text: 'Quarterly', value: 'quarterly' },
              { text: 'Yearly', value: 'yearly' },
              { text: 'Lifetime', value: 'lifetime' }
            ]
          },
          sort: 15,
          width: 'half',
          validation: { _in: ['monthly', 'quarterly', 'yearly', 'lifetime'] }
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'monthly'
        }
      },
      {
        field: 'membershipfeatures',
        type: 'json',
        meta: {
          interface: 'tags',
          options: {
            presets: [
              'unlimited-properties', 'premium-support', 'analytics',
              'featured-listings', 'priority-placement', 'custom-branding'
            ]
          },
          sort: 16,
          width: 'full',
          note: 'Renamed from features to avoid conflicts'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'max_properties',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 17,
          width: 'half',
          validation: {
            _and: [{ max_properties: { _gte: 1 } }]
          }
        },
        schema: {
          is_nullable: true,
          default_value: 10
        }
      },
      {
        field: 'max_images',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 18,
          width: 'half',
          validation: {
            _and: [{ max_images: { _gte: 1 } }]
          }
        },
        schema: {
          is_nullable: true,
          default_value: 5
        }
      },
      {
        field: 'can_create_projects',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 19,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'can_access_analytics',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 20,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'can_use_premium_filters',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 21,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'priority',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 22,
          width: 'half',
          note: 'Display priority (higher = first)'
        },
        schema: {
          is_nullable: true,
          default_value: 0
        }
      },
      {
        field: 'is_active',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 23,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: true
        }
      }
    ],

    // Projects fields with proper choices and fixed field name conflicts
    projects: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Draft', value: 'draft' },
              { text: 'Published', value: 'published' },
              { text: 'Archived', value: 'archived' }
            ]
          },
          sort: 1,
          width: 'half',
          note: 'Directus workflow status'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'draft'
        }
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
          special: ['sort']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_created',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_updated',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'title',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 10,
          width: 'full',
          note: 'Project title/name'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'description',
        type: 'text',
        meta: {
          interface: 'input-rich-text-html',
          sort: 11,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'developer',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 12,
          width: 'half'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'project_type',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Residential', value: 'residential' },
              { text: 'Commercial', value: 'commercial' },
              { text: 'Mixed Use', value: 'mixed-use' },
              { text: 'Industrial', value: 'industrial' }
            ]
          },
          required: true,
          sort: 13,
          width: 'half',
          validation: { _in: ['residential', 'commercial', 'mixed-use', 'industrial'] }
        },
        schema: {
          max_length: 50,
          is_nullable: false
        }
      },
      {
        field: 'projectstatus',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Planning', value: 'planning' },
              { text: 'Under Construction', value: 'under-construction' },
              { text: 'Completed', value: 'completed' },
              { text: 'On Hold', value: 'on-hold' },
              { text: 'Cancelled', value: 'cancelled' }
            ]
          },
          sort: 14,
          width: 'half',
          note: 'Project development status (renamed from status to avoid conflicts)',
          validation: { _in: ['planning', 'under-construction', 'completed', 'on-hold', 'cancelled'] }
        },
        schema: {
          max_length: 50,
          is_nullable: true,
          default_value: 'planning'
        }
      },
      {
        field: 'start_date',
        type: 'date',
        meta: {
          interface: 'datetime',
          sort: 15,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'completion_date',
        type: 'date',
        meta: {
          interface: 'datetime',
          sort: 16,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'total_units',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 17,
          width: 'half',
          validation: {
            _and: [{ total_units: { _gte: 1 } }]
          }
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'available_units',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 18,
          width: 'half',
          validation: {
            _and: [{ available_units: { _gte: 0 } }]
          }
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'min_price',
        type: 'decimal',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          display_options: {
            decimals: 2,
            prefix: '$'
          },
          sort: 19,
          width: 'half'
        },
        schema: {
          numeric_precision: 15,
          numeric_scale: 2,
          is_nullable: true
        }
      },
      {
        field: 'max_price',
        type: 'decimal',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          display_options: {
            decimals: 2,
            prefix: '$'
          },
          sort: 20,
          width: 'half'
        },
        schema: {
          numeric_precision: 15,
          numeric_scale: 2,
          is_nullable: true
        }
      },
      {
        field: 'currency',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'US Dollar', value: 'USD' },
              { text: 'Euro', value: 'EUR' },
              { text: 'British Pound', value: 'GBP' },
              { text: 'UAE Dirham', value: 'AED' },
              { text: 'Saudi Riyal', value: 'SAR' }
            ]
          },
          sort: 21,
          width: 'half',
          validation: { _in: ['USD', 'EUR', 'GBP', 'AED', 'SAR'] }
        },
        schema: {
          max_length: 3,
          is_nullable: true,
          default_value: 'USD'
        }
      },
      {
        field: 'address',
        type: 'text',
        meta: {
          interface: 'input-multiline',
          required: true,
          sort: 22,
          width: 'full'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'city',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 23,
          width: 'half'
        },
        schema: {
          max_length: 100,
          is_nullable: false
        }
      },
      {
        field: 'country',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 24,
          width: 'half'
        },
        schema: {
          max_length: 100,
          is_nullable: false
        }
      },
      {
        field: 'latitude',
        type: 'float',
        meta: {
          interface: 'input',
          sort: 25,
          width: 'half',
          validation: {
            _and: [
              { latitude: { _gte: -90 } },
              { latitude: { _lte: 90 } }
            ]
          }
        },
        schema: {
          numeric_precision: 10,
          numeric_scale: 8,
          is_nullable: true
        }
      },
      {
        field: 'longitude',
        type: 'float',
        meta: {
          interface: 'input',
          sort: 26,
          width: 'half',
          validation: {
            _and: [
              { longitude: { _gte: -180 } },
              { longitude: { _lte: 180 } }
            ]
          }
        },
        schema: {
          numeric_precision: 11,
          numeric_scale: 8,
          is_nullable: true
        }
      },
      {
        field: 'amenities',
        type: 'json',
        meta: {
          interface: 'tags',
          options: {
            presets: [
              'swimming-pool', 'gym', 'spa', 'playground', 'garden',
              'security', 'parking', 'concierge', 'shopping', 'restaurants'
            ]
          },
          sort: 27,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'virtual_tour',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 28,
          width: 'full',
          note: 'Virtual tour URL'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'payment_plan',
        type: 'text',
        meta: {
          interface: 'input-rich-text-html',
          sort: 29,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'featured',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 30,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'slug',
        type: 'string',
        meta: {
          interface: 'input',
          options: { slug: true },
          required: true,
          sort: 31,
          width: 'full',
          validation: {
            _regex: '^[a-z0-9-]+$'
          }
        },
        schema: {
          max_length: 255,
          is_nullable: false,
          is_unique: true
        }
      },
      {
        field: 'images',
        type: 'alias',
        meta: {
          interface: 'files',
          special: ['files'],
          sort: 32,
          width: 'full',
          note: 'Project images and gallery'
        }
      },
      {
        field: 'floor_plans',
        type: 'alias',
        meta: {
          interface: 'files',
          special: ['files'],
          sort: 33,
          width: 'full',
          note: 'Floor plan images and documents'
        }
      },
      {
        field: 'brochure',
        type: 'alias',
        meta: {
          interface: 'file',
          special: ['file'],
          sort: 34,
          width: 'half',
          note: 'Project brochure document'
        }
      }
    ],

    // Properties fields with proper choices and fixed field name conflicts
    properties: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Draft', value: 'draft' },
              { text: 'Published', value: 'published' },
              { text: 'Archived', value: 'archived' }
            ]
          },
          sort: 1,
          width: 'half',
          note: 'Directus workflow status'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'draft'
        }
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
          special: ['sort']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_created',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_updated',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'title',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 10,
          width: 'full'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'description',
        type: 'text',
        meta: {
          interface: 'input-rich-text-html',
          sort: 11,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'price',
        type: 'decimal',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          display_options: {
            decimals: 2,
            prefix: '$'
          },
          required: true,
          sort: 12,
          width: 'half',
          validation: {
            _and: [{ price: { _gte: 0 } }]
          }
        },
        schema: {
          numeric_precision: 15,
          numeric_scale: 2,
          is_nullable: false
        }
      },
      {
        field: 'currency',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'US Dollar', value: 'USD' },
              { text: 'Euro', value: 'EUR' },
              { text: 'British Pound', value: 'GBP' },
              { text: 'UAE Dirham', value: 'AED' },
              { text: 'Saudi Riyal', value: 'SAR' }
            ]
          },
          sort: 13,
          width: 'half',
          validation: { _in: ['USD', 'EUR', 'GBP', 'AED', 'SAR'] }
        },
        schema: {
          max_length: 3,
          is_nullable: true,
          default_value: 'USD'
        }
      },
      {
        field: 'property_type',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Apartment', value: 'apartment' },
              { text: 'Villa', value: 'villa' },
              { text: 'Townhouse', value: 'townhouse' },
              { text: 'Penthouse', value: 'penthouse' },
              { text: 'Studio', value: 'studio' },
              { text: 'Duplex', value: 'duplex' },
              { text: 'Land', value: 'land' },
              { text: 'Commercial', value: 'commercial' }
            ]
          },
          required: true,
          sort: 14,
          width: 'half',
          validation: { _in: ['apartment', 'villa', 'townhouse', 'penthouse', 'studio', 'duplex', 'land', 'commercial'] }
        },
        schema: {
          max_length: 50,
          is_nullable: false
        }
      },
      {
        field: 'propertyoffer',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'For Sale', value: 'for-sale' },
              { text: 'For Rent', value: 'for-rent' },
              { text: 'Sold', value: 'sold' },
              { text: 'Rented', value: 'rented' },
              { text: 'Off Market', value: 'off-market' }
            ]
          },
          required: true,
          sort: 15,
          width: 'half',
          note: 'Renamed from offer to avoid conflicts',
          validation: { _in: ['for-sale', 'for-rent', 'sold', 'rented', 'off-market'] }
        },
        schema: {
          max_length: 20,
          is_nullable: false
        }
      },
      {
        field: 'area',
        type: 'decimal',
        meta: {
          interface: 'input',
          required: true,
          sort: 16,
          width: 'half'
        },
        schema: {
          numeric_precision: 10,
          numeric_scale: 2,
          is_nullable: false
        }
      },
      {
        field: 'area_unit',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Square Feet', value: 'sqft' },
              { text: 'Square Meters', value: 'sqm' }
            ]
          },
          sort: 17,
          width: 'half',
          validation: { _in: ['sqft', 'sqm'] }
        },
        schema: {
          max_length: 10,
          is_nullable: true,
          default_value: 'sqft'
        }
      },
      {
        field: 'bedrooms',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 18,
          width: 'half',
          validation: {
            _and: [
              { bedrooms: { _gte: 0 } },
              { bedrooms: { _lte: 20 } }
            ]
          }
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'bathrooms',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 19,
          width: 'half',
          validation: {
            _and: [
              { bathrooms: { _gte: 0 } },
              { bathrooms: { _lte: 20 } }
            ]
          }
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'address',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 20,
          width: 'full'
        },
        schema: {
          max_length: 500,
          is_nullable: false
        }
      },
      {
        field: 'city',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 21,
          width: 'half'
        },
        schema: {
          max_length: 100,
          is_nullable: false
        }
      },
      {
        field: 'country',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 22,
          width: 'half'
        },
        schema: {
          max_length: 100,
          is_nullable: false
        }
      },
      {
        field: 'neighborhood',
        type: 'json',
        meta: {
          interface: 'input-code',
          options: {
            language: 'json'
          },
          sort: 23,
          width: 'full',
          note: 'Neighborhood information as JSON'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'coordinates',
        type: 'json',
        meta: {
          interface: 'input-code',
          options: {
            language: 'json'
          },
          sort: 24,
          width: 'full',
          note: 'GPS coordinates as JSON'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'nearby_places',
        type: 'json',
        meta: {
          interface: 'input-code',
          options: {
            language: 'json'
          },
          sort: 25,
          width: 'full',
          note: 'Nearby places data as JSON'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'latitude',
        type: 'float',
        meta: {
          interface: 'input',
          sort: 26,
          width: 'half',
          validation: {
            _and: [
              { latitude: { _gte: -90 } },
              { latitude: { _lte: 90 } }
            ]
          }
        },
        schema: {
          numeric_precision: 10,
          numeric_scale: 8,
          is_nullable: true
        }
      },
      {
        field: 'longitude',
        type: 'float',
        meta: {
          interface: 'input',
          sort: 27,
          width: 'half',
          validation: {
            _and: [
              { longitude: { _gte: -180 } },
              { longitude: { _lte: 180 } }
            ]
          }
        },
        schema: {
          numeric_precision: 11,
          numeric_scale: 8,
          is_nullable: true
        }
      },
      {
        field: 'property_code',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 28,
          width: 'half',
          note: 'Unique property identifier'
        },
        schema: {
          max_length: 100,
          is_nullable: true,
          is_unique: true
        }
      },
      {
        field: 'propertyfeatures',
        type: 'json',
        meta: {
          interface: 'tags',
          options: {
            presets: [
              'balcony', 'terrace', 'garden', 'pool', 'gym', 'parking',
              'elevator', 'security', 'furnished', 'air-conditioning'
            ]
          },
          sort: 29,
          width: 'full',
          note: 'Renamed from features to avoid conflicts'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'is_luxury',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 30,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'year_built',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 31,
          width: 'half',
          validation: {
            _and: [
              { year_built: { _gte: 1800 } },
              { year_built: { _lte: 2030 } }
            ]
          }
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'parking',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 32,
          width: 'half',
          validation: {
            _and: [{ parking: { _gte: 0 } }]
          },
          note: 'Number of parking spaces'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'furnished',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 33,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'pet_friendly',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 34,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'featured',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 35,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'views',
        type: 'integer',
        meta: {
          interface: 'input',
          readonly: true,
          sort: 36,
          width: 'half',
          note: 'View count for analytics'
        },
        schema: {
          is_nullable: true,
          default_value: 0
        }
      },
      {
        field: 'virtual_tour',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 37,
          width: 'full',
          note: 'Virtual tour URL'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'slug',
        type: 'string',
        meta: {
          interface: 'input',
          options: { slug: true },
          required: true,
          sort: 38,
          width: 'full',
          validation: {
            _regex: '^[a-z0-9-]+$'
          }
        },
        schema: {
          max_length: 255,
          is_nullable: false,
          is_unique: true
        }
      },
      {
        field: 'owner_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 39,
          width: 'half',
          note: 'Property owner'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'agent_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 40,
          width: 'half',
          note: 'Property agent'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'project_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 41,
          width: 'half',
          note: 'Related project'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'images',
        type: 'alias',
        meta: {
          interface: 'files',
          special: ['files'],
          sort: 42,
          width: 'full',
          note: 'Property images and gallery'
        }
      },
      {
        field: 'floor_plan',
        type: 'alias',
        meta: {
          interface: 'file',
          special: ['file'],
          sort: 43,
          width: 'half',
          note: 'Floor plan image or document'
        }
      }
    ],

    // Nearby Place Categories fields with proper choices
    nearby_place_categories: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Draft', value: 'draft' },
              { text: 'Published', value: 'published' },
              { text: 'Archived', value: 'archived' }
            ]
          },
          sort: 1,
          width: 'half',
          note: 'Directus workflow status'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'published'
        }
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
          special: ['sort']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_created',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_updated',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'name',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 10,
          width: 'half',
          validation: {
            _regex: '^[a-z0-9_-]+$'
          },
          note: 'Internal name (lowercase, no spaces)'
        },
        schema: {
          max_length: 255,
          is_nullable: false,
          is_unique: true
        }
      },
      {
        field: 'display_name',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 11,
          width: 'half',
          note: 'User-friendly display name'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'description',
        type: 'text',
        meta: {
          interface: 'input-multiline',
          sort: 12,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'icon',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 13,
          width: 'half',
          note: 'Icon name or URL'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'google_place_types',
        type: 'json',
        meta: {
          interface: 'tags',
          options: {
            presets: [
              'restaurant', 'cafe', 'school', 'hospital', 'pharmacy',
              'bank', 'atm', 'gas_station', 'shopping_mall', 'supermarket',
              'gym', 'park', 'library', 'museum', 'movie_theater',
              'bus_station', 'subway_station', 'airport', 'taxi_stand'
            ]
          },
          required: true,
          sort: 14,
          width: 'full',
          note: 'Google Places API types'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'enabled',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 15,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: true
        }
      },
      {
        field: 'search_radius',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 16,
          width: 'half',
          validation: {
            _and: [
              { search_radius: { _gte: 100 } },
              { search_radius: { _lte: 5000 } }
            ]
          },
          note: 'Search radius in meters (100-5000)'
        },
        schema: {
          is_nullable: true,
          default_value: 1000
        }
      },
      {
        field: 'max_results',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 17,
          width: 'half',
          validation: {
            _and: [
              { max_results: { _gte: 1 } },
              { max_results: { _lte: 20 } }
            ]
          },
          note: 'Maximum results to return (1-20)'
        },
        schema: {
          is_nullable: true,
          default_value: 10
        }
      },
      {
        field: 'priority',
        type: 'integer',
        meta: {
          interface: 'input',
          sort: 18,
          width: 'half',
          note: 'Display priority (higher = first)'
        },
        schema: {
          is_nullable: true,
          default_value: 0
        }
      },
      {
        field: 'color',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 19,
          width: 'half',
          validation: {
            _regex: '^#[0-9A-Fa-f]{6}$'
          },
          note: 'Hex color code'
        },
        schema: {
          max_length: 7,
          is_nullable: true,
          default_value: '#3B82F6'
        }
      }
    ],

    // Messages fields with proper choices and file attachments
    messages: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Draft', value: 'draft' },
              { text: 'Published', value: 'published' },
              { text: 'Archived', value: 'archived' }
            ]
          },
          sort: 1,
          width: 'half',
          note: 'Directus workflow status'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'published'
        }
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
          special: ['sort']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_created',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_updated',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'subject',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 10,
          width: 'full'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'content',
        type: 'text',
        meta: {
          interface: 'input-rich-text-html',
          required: true,
          sort: 11,
          width: 'full'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'message_type',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'General', value: 'general' },
              { text: 'Inquiry', value: 'inquiry' },
              { text: 'Property Inquiry', value: 'property-inquiry' },
              { text: 'Project Inquiry', value: 'project-inquiry' }
            ]
          },
          sort: 12,
          width: 'half',
          validation: { _in: ['general', 'inquiry', 'property-inquiry', 'project-inquiry'] }
        },
        schema: {
          max_length: 50,
          is_nullable: true,
          default_value: 'general'
        }
      },
      {
        field: 'sender_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          required: true,
          sort: 13,
          width: 'half'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'recipient_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          required: true,
          sort: 14,
          width: 'half'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'property_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 15,
          width: 'half',
          note: 'Related property (optional)'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'project_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 16,
          width: 'half',
          note: 'Related project (optional)'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'parent_message_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 17,
          width: 'half',
          note: 'Parent message for replies'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'is_read',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 18,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'read_at',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          sort: 19,
          width: 'half',
          note: 'When message was read'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'attachments',
        type: 'alias',
        meta: {
          interface: 'files',
          special: ['files'],
          sort: 20,
          width: 'full',
          note: 'Message attachments'
        }
      }
    ],

    // Notifications fields with proper choices
    notifications: [
      {
        field: 'id',
        type: 'uuid',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input',
          special: ['uuid']
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: false,
          is_nullable: false
        }
      },
      {
        field: 'status',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Draft', value: 'draft' },
              { text: 'Published', value: 'published' },
              { text: 'Archived', value: 'archived' }
            ]
          },
          sort: 1,
          width: 'half',
          note: 'Directus workflow status'
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'published'
        }
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
          special: ['sort']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_created',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_created',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-created']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'user_updated',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'user',
          readonly: true,
          hidden: true,
          special: ['user-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'date_updated',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          readonly: true,
          hidden: true,
          special: ['date-updated']
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'title',
        type: 'string',
        meta: {
          interface: 'input',
          required: true,
          sort: 10,
          width: 'full'
        },
        schema: {
          max_length: 255,
          is_nullable: false
        }
      },
      {
        field: 'message',
        type: 'text',
        meta: {
          interface: 'input-multiline',
          required: true,
          sort: 11,
          width: 'full'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'type',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Info', value: 'info' },
              { text: 'Success', value: 'success' },
              { text: 'Warning', value: 'warning' },
              { text: 'Error', value: 'error' },
              { text: 'Property Inquiry', value: 'property_inquiry' },
              { text: 'Property Approved', value: 'property_approved' },
              { text: 'Property Rejected', value: 'property_rejected' },
              { text: 'Message Received', value: 'message_received' },
              { text: 'System', value: 'system' }
            ]
          },
          required: true,
          sort: 12,
          width: 'half',
          validation: { _in: ['info', 'success', 'warning', 'error', 'property_inquiry', 'property_approved', 'property_rejected', 'message_received', 'system'] }
        },
        schema: {
          max_length: 50,
          is_nullable: false,
          default_value: 'info'
        }
      },
      {
        field: 'priority',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'labels',
          options: {
            choices: [
              { text: 'Low', value: 'low' },
              { text: 'Normal', value: 'normal' },
              { text: 'High', value: 'high' },
              { text: 'Urgent', value: 'urgent' }
            ]
          },
          sort: 13,
          width: 'half',
          validation: { _in: ['low', 'normal', 'high', 'urgent'] }
        },
        schema: {
          max_length: 20,
          is_nullable: true,
          default_value: 'normal'
        }
      },
      {
        field: 'recipient_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          required: true,
          sort: 14,
          width: 'half'
        },
        schema: {
          is_nullable: false
        }
      },
      {
        field: 'sender_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 15,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'related_property_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 16,
          width: 'half',
          note: 'Related property (optional)'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'related_project_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 17,
          width: 'half',
          note: 'Related project (optional)'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'related_message_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          sort: 18,
          width: 'half',
          note: 'Related message (optional)'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'action_url',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 19,
          width: 'full',
          note: 'URL for notification action'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'is_read',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 20,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'read_at',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          sort: 21,
          width: 'half',
          note: 'When notification was read'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'expires_at',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          sort: 22,
          width: 'half',
          note: 'When notification expires'
        },
        schema: {
          is_nullable: true
        }
      }
    ],

    // Junction table fields for file relationships
    properties_files: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input'
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
          is_nullable: false
        }
      },
      {
        field: 'properties_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'directus_files_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          is_nullable: true
        }
      }
    ],

    projects_files: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input'
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
          is_nullable: false
        }
      },
      {
        field: 'projects_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'directus_files_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          is_nullable: true
        }
      }
    ],

    messages_files: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
          readonly: true,
          interface: 'input'
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
          is_nullable: false
        }
      },
      {
        field: 'messages_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'directus_files_id',
        type: 'uuid',
        meta: {
          hidden: true,
          interface: 'select-dropdown-m2o'
        },
        schema: {
          is_nullable: true
        }
      }
    ]
  },

  relations: [
    // User relations
    {
      collection: 'properties',
      field: 'owner_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'properties',
        many_field: 'owner_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'properties',
        column: 'owner_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'properties',
      field: 'agent_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'properties',
        many_field: 'agent_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'properties',
        column: 'agent_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'properties',
      field: 'project_id',
      related_collection: 'projects',
      meta: {
        many_collection: 'properties',
        many_field: 'project_id',
        one_collection: 'projects',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'properties',
        column: 'project_id',
        foreign_key_table: 'projects',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    // Message relations
    {
      collection: 'messages',
      field: 'sender_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'messages',
        many_field: 'sender_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'messages',
        column: 'sender_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'messages',
      field: 'recipient_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'messages',
        many_field: 'recipient_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'messages',
        column: 'recipient_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'messages',
      field: 'property_id',
      related_collection: 'properties',
      meta: {
        many_collection: 'messages',
        many_field: 'property_id',
        one_collection: 'properties',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'messages',
        column: 'property_id',
        foreign_key_table: 'properties',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'messages',
      field: 'project_id',
      related_collection: 'projects',
      meta: {
        many_collection: 'messages',
        many_field: 'project_id',
        one_collection: 'projects',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'messages',
        column: 'project_id',
        foreign_key_table: 'projects',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'messages',
      field: 'parent_message_id',
      related_collection: 'messages',
      meta: {
        many_collection: 'messages',
        many_field: 'parent_message_id',
        one_collection: 'messages',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'messages',
        column: 'parent_message_id',
        foreign_key_table: 'messages',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    // Notification relations
    {
      collection: 'notifications',
      field: 'recipient_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'notifications',
        many_field: 'recipient_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'notifications',
        column: 'recipient_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'notifications',
      field: 'sender_id',
      related_collection: 'directus_users',
      meta: {
        many_collection: 'notifications',
        many_field: 'sender_id',
        one_collection: 'directus_users',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'notifications',
        column: 'sender_id',
        foreign_key_table: 'directus_users',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'notifications',
      field: 'related_property_id',
      related_collection: 'properties',
      meta: {
        many_collection: 'notifications',
        many_field: 'related_property_id',
        one_collection: 'properties',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'notifications',
        column: 'related_property_id',
        foreign_key_table: 'properties',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'notifications',
      field: 'related_project_id',
      related_collection: 'projects',
      meta: {
        many_collection: 'notifications',
        many_field: 'related_project_id',
        one_collection: 'projects',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'notifications',
        column: 'related_project_id',
        foreign_key_table: 'projects',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    {
      collection: 'notifications',
      field: 'related_message_id',
      related_collection: 'messages',
      meta: {
        many_collection: 'notifications',
        many_field: 'related_message_id',
        one_collection: 'messages',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: 'nullify'
      },
      schema: {
        table: 'notifications',
        column: 'related_message_id',
        foreign_key_table: 'messages',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'SET NULL'
      }
    },
    // File junction table relations
    {
      collection: 'properties_files',
      field: 'properties_id',
      related_collection: 'properties',
      meta: {
        many_collection: 'properties_files',
        many_field: 'properties_id',
        one_collection: 'properties',
        one_field: 'images',
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: 'directus_files_id',
        sort_field: null,
        one_deselect_action: 'delete'
      },
      schema: {
        table: 'properties_files',
        column: 'properties_id',
        foreign_key_table: 'properties',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'CASCADE'
      }
    },
    {
      collection: 'properties_files',
      field: 'directus_files_id',
      related_collection: 'directus_files',
      meta: {
        many_collection: 'properties_files',
        many_field: 'directus_files_id',
        one_collection: 'directus_files',
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: 'properties_id',
        sort_field: null,
        one_deselect_action: 'delete'
      },
      schema: {
        table: 'properties_files',
        column: 'directus_files_id',
        foreign_key_table: 'directus_files',
        foreign_key_column: 'id',
        constraint_name: null,
        on_update: 'NO ACTION',
        on_delete: 'CASCADE'
      }
    }
  ]
};

module.exports = { schemaDefinitions };
