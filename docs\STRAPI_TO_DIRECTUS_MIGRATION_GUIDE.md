# Strapi v5 to Directus CMS Migration Guide
## Complete Real Estate Platform Migration Documentation

### Table of Contents
1. [Migration Overview](#migration-overview)
2. [Database Schema Migration](#database-schema-migration)
3. [Backend File-by-File Migration](#backend-file-by-file-migration)
4. [Custom Logic Preservation](#custom-logic-preservation)
5. [Frontend Integration Changes](#frontend-integration-changes)
6. [Step-by-Step Implementation](#step-by-step-implementation)

---

## 1. Migration Overview

### Current Strapi v5 Architecture
```
backend/
├── config/
│   ├── database.ts
│   ├── plugins.ts
│   ├── middlewares.ts
│   └── server.ts
├── src/
│   ├── api/
│   │   ├── property/
│   │   ├── project/
│   │   ├── membership/
│   │   ├── message/
│   │   ├── notification/
│   │   └── nearby-place-category/
│   ├── components/
│   └── index.ts
└── types/
```

### Target Directus Architecture
```
directus/
├── extensions/
│   ├── hooks/
│   ├── endpoints/
│   ├── operations/
│   └── interfaces/
├── migrations/
├── schema/
└── uploads/
```

### Migration Timeline
- **Phase 1:** Database Schema Migration (Week 1-2)
- **Phase 2:** Backend Logic Migration (Week 3-5)
- **Phase 3:** Frontend Integration (Week 6-8)
- **Phase 4:** Testing & Optimization (Week 9-10)

---

## 2. Database Schema Migration

### 2.1 Content Types to Collections Mapping

#### Property Collection
**Strapi Schema → Directus Collection**

```json
// Strapi: backend/src/api/property/content-types/property/schema.json
{
  "collectionName": "properties",
  "attributes": {
    "title": { "type": "string", "required": true },
    "description": { "type": "richtext" },
    "price": { "type": "decimal", "required": true },
    "currency": { "type": "enumeration", "enum": ["USD", "EUR", "GBP", "AED", "SAR"] },
    "propertyType": { "type": "enumeration", "enum": ["apartment", "villa", "townhouse"] },
    "offer": { "type": "enumeration", "enum": ["for-sale", "for-rent", "sold"] },
    "bedrooms": { "type": "integer", "min": 0 },
    "bathrooms": { "type": "integer", "min": 0 },
    "area": { "type": "decimal", "required": true },
    "coordinates": { "type": "json" },
    "nearbyPlaces": { "type": "json" },
    "features": { "type": "json" },
    "images": { "type": "media", "multiple": true },
    "owner": { "type": "relation", "target": "plugin::users-permissions.user" },
    "views": { "type": "integer", "default": 0 },
    "slug": { "type": "uid", "targetField": "title" }
  }
}
```

```sql
-- Directus Migration: migrations/001_create_properties.sql
CREATE TABLE properties (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    property_type VARCHAR(50) NOT NULL,
    offer VARCHAR(50) NOT NULL,
    bedrooms INTEGER DEFAULT 0,
    bathrooms INTEGER DEFAULT 0,
    area DECIMAL(10,2) NOT NULL,
    area_unit VARCHAR(10) DEFAULT 'sqft',
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    coordinates JSON,
    nearby_places JSON,
    features JSON,
    is_luxury BOOLEAN DEFAULT false,
    virtual_tour TEXT,
    year_built INTEGER,
    parking INTEGER DEFAULT 0,
    furnished BOOLEAN DEFAULT false,
    pet_friendly BOOLEAN DEFAULT false,
    featured BOOLEAN DEFAULT false,
    views INTEGER DEFAULT 0,
    slug VARCHAR(255) UNIQUE NOT NULL,
    owner_id UUID REFERENCES directus_users(id),
    agent_id UUID REFERENCES directus_users(id),
    project_id UUID REFERENCES projects(id),
    status VARCHAR(20) DEFAULT 'draft',
    date_created TIMESTAMP DEFAULT NOW(),
    date_updated TIMESTAMP DEFAULT NOW(),
    user_created UUID REFERENCES directus_users(id),
    user_updated UUID REFERENCES directus_users(id)
);

-- Indexes for performance
CREATE INDEX idx_properties_owner ON properties(owner_id);
CREATE INDEX idx_properties_city ON properties(city);
CREATE INDEX idx_properties_type ON properties(property_type);
CREATE INDEX idx_properties_offer ON properties(offer);
CREATE INDEX idx_properties_price ON properties(price);
CREATE INDEX idx_properties_featured ON properties(featured);
CREATE INDEX idx_properties_status ON properties(status);
```

#### Project Collection
```sql
-- Directus Migration: migrations/002_create_projects.sql
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    developer VARCHAR(255),
    status VARCHAR(50) DEFAULT 'planning',
    completion_date DATE,
    starting_price DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'USD',
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    country VARCHAR(100) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    amenities JSON,
    virtual_tour TEXT,
    payment_plan TEXT,
    featured BOOLEAN DEFAULT false,
    slug VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'draft',
    date_created TIMESTAMP DEFAULT NOW(),
    date_updated TIMESTAMP DEFAULT NOW(),
    user_created UUID REFERENCES directus_users(id),
    user_updated UUID REFERENCES directus_users(id)
);
```

#### Membership Collection
```sql
-- Directus Migration: migrations/003_create_memberships.sql
CREATE TABLE memberships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    price DECIMAL(10,2) NOT NULL DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    duration VARCHAR(20) DEFAULT 'monthly',
    features JSON DEFAULT '[]',
    max_properties INTEGER DEFAULT 10,
    max_images INTEGER DEFAULT 5,
    can_create_projects BOOLEAN DEFAULT false,
    can_access_analytics BOOLEAN DEFAULT false,
    can_use_premium_filters BOOLEAN DEFAULT false,
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'published',
    date_created TIMESTAMP DEFAULT NOW(),
    date_updated TIMESTAMP DEFAULT NOW()
);
```

#### User Extensions
```sql
-- Directus Migration: migrations/004_extend_users.sql
ALTER TABLE directus_users ADD COLUMN first_name VARCHAR(255);
ALTER TABLE directus_users ADD COLUMN last_name VARCHAR(255);
ALTER TABLE directus_users ADD COLUMN phone VARCHAR(50);
ALTER TABLE directus_users ADD COLUMN company VARCHAR(255);
ALTER TABLE directus_users ADD COLUMN job_title VARCHAR(255);
ALTER TABLE directus_users ADD COLUMN bio TEXT;
ALTER TABLE directus_users ADD COLUMN is_agent BOOLEAN DEFAULT false;
ALTER TABLE directus_users ADD COLUMN membership_id UUID REFERENCES memberships(id);
ALTER TABLE directus_users ADD COLUMN membership_expires_at TIMESTAMP;
```

#### Message Collection
```sql
-- Directus Migration: migrations/005_create_messages.sql
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subject VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    sender_id UUID REFERENCES directus_users(id),
    recipient_id UUID REFERENCES directus_users(id),
    property_id UUID REFERENCES properties(id),
    project_id UUID REFERENCES projects(id),
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP,
    message_type VARCHAR(50) DEFAULT 'general',
    parent_message_id UUID REFERENCES messages(id),
    status VARCHAR(20) DEFAULT 'active',
    date_created TIMESTAMP DEFAULT NOW(),
    date_updated TIMESTAMP DEFAULT NOW(),
    user_created UUID REFERENCES directus_users(id),
    user_updated UUID REFERENCES directus_users(id)
);

-- Indexes for performance
CREATE INDEX idx_messages_sender ON messages(sender_id);
CREATE INDEX idx_messages_recipient ON messages(recipient_id);
CREATE INDEX idx_messages_property ON messages(property_id);
CREATE INDEX idx_messages_project ON messages(project_id);
CREATE INDEX idx_messages_parent ON messages(parent_message_id);
CREATE INDEX idx_messages_type ON messages(message_type);
CREATE INDEX idx_messages_read ON messages(is_read);

-- Message attachments junction table
CREATE TABLE messages_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    messages_id UUID REFERENCES messages(id) ON DELETE CASCADE,
    directus_files_id UUID REFERENCES directus_files(id) ON DELETE CASCADE,
    UNIQUE(messages_id, directus_files_id)
);
```

#### Notification Collection
```sql
-- Directus Migration: migrations/006_create_notifications.sql
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info',
    is_read BOOLEAN DEFAULT false,
    read_at TIMESTAMP,
    recipient_id UUID REFERENCES directus_users(id) NOT NULL,
    sender_id UUID REFERENCES directus_users(id),
    related_property_id UUID REFERENCES properties(id),
    related_project_id UUID REFERENCES projects(id),
    related_message_id UUID REFERENCES messages(id),
    action_url VARCHAR(500),
    action_text VARCHAR(100),
    metadata JSON,
    priority VARCHAR(20) DEFAULT 'normal',
    expires_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active',
    date_created TIMESTAMP DEFAULT NOW(),
    date_updated TIMESTAMP DEFAULT NOW(),
    user_created UUID REFERENCES directus_users(id),
    user_updated UUID REFERENCES directus_users(id)
);

-- Indexes for performance
CREATE INDEX idx_notifications_recipient ON notifications(recipient_id);
CREATE INDEX idx_notifications_sender ON notifications(sender_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_read ON notifications(is_read);
CREATE INDEX idx_notifications_priority ON notifications(priority);
CREATE INDEX idx_notifications_expires ON notifications(expires_at);
```

#### Nearby Place Category Collection
```sql
-- Directus Migration: migrations/007_create_nearby_place_categories.sql
CREATE TABLE nearby_place_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(255) NOT NULL,
    google_place_types JSON NOT NULL,
    enabled BOOLEAN DEFAULT true,
    search_radius INTEGER DEFAULT 1000,
    max_results INTEGER DEFAULT 10,
    priority INTEGER DEFAULT 0,
    color VARCHAR(7) DEFAULT '#3B82F6',
    status VARCHAR(20) DEFAULT 'active',
    date_created TIMESTAMP DEFAULT NOW(),
    date_updated TIMESTAMP DEFAULT NOW(),
    user_created UUID REFERENCES directus_users(id),
    user_updated UUID REFERENCES directus_users(id),

    -- Constraints
    CONSTRAINT chk_search_radius CHECK (search_radius >= 100 AND search_radius <= 5000),
    CONSTRAINT chk_max_results CHECK (max_results >= 1 AND max_results <= 20)
);

-- Indexes for performance
CREATE INDEX idx_nearby_categories_enabled ON nearby_place_categories(enabled);
CREATE INDEX idx_nearby_categories_priority ON nearby_place_categories(priority);
CREATE INDEX idx_nearby_categories_name ON nearby_place_categories(name);
```

### 2.2 Data Migration Scripts

#### Property Data Migration
```sql
-- Migration Script: migrate_properties.sql
INSERT INTO properties (
    id, title, description, price, currency, property_type, offer,
    bedrooms, bathrooms, area, area_unit, address, city, country,
    coordinates, nearby_places, features, is_luxury, virtual_tour,
    year_built, parking, furnished, pet_friendly, featured, views,
    slug, owner_id, agent_id, project_id, status, date_created, date_updated
)
SELECT 
    gen_random_uuid(),
    title,
    description,
    price,
    currency,
    property_type,
    offer,
    bedrooms,
    bathrooms,
    area,
    area_unit,
    address,
    city,
    country,
    coordinates::json,
    nearby_places::json,
    features::json,
    is_luxury,
    virtual_tour,
    year_built,
    parking,
    furnished,
    pet_friendly,
    featured,
    views,
    slug,
    (SELECT id FROM directus_users WHERE email = strapi_users.email),
    (SELECT id FROM directus_users WHERE email = strapi_agents.email),
    (SELECT id FROM projects WHERE slug = strapi_projects.slug),
    CASE WHEN published_at IS NOT NULL THEN 'published' ELSE 'draft' END,
    created_at,
    updated_at
FROM strapi_properties
LEFT JOIN strapi_users ON strapi_properties.owner_id = strapi_users.id
LEFT JOIN strapi_users strapi_agents ON strapi_properties.agent_id = strapi_agents.id
LEFT JOIN strapi_projects ON strapi_properties.project_id = strapi_projects.id;
```

#### Message Data Migration
```sql
-- Migration Script: migrate_messages.sql
INSERT INTO messages (
    id, subject, content, sender_id, recipient_id, property_id, project_id,
    is_read, read_at, message_type, parent_message_id, status, date_created, date_updated
)
SELECT
    gen_random_uuid(),
    subject,
    content,
    (SELECT id FROM directus_users WHERE email = sender_users.email),
    (SELECT id FROM directus_users WHERE email = recipient_users.email),
    (SELECT id FROM properties WHERE slug = strapi_properties.slug),
    (SELECT id FROM projects WHERE slug = strapi_projects.slug),
    is_read,
    read_at,
    message_type,
    (SELECT id FROM messages WHERE subject = parent_messages.subject LIMIT 1),
    'active',
    created_at,
    updated_at
FROM strapi_messages
LEFT JOIN strapi_users sender_users ON strapi_messages.sender_id = sender_users.id
LEFT JOIN strapi_users recipient_users ON strapi_messages.recipient_id = recipient_users.id
LEFT JOIN strapi_properties ON strapi_messages.property_id = strapi_properties.id
LEFT JOIN strapi_projects ON strapi_messages.project_id = strapi_projects.id
LEFT JOIN strapi_messages parent_messages ON strapi_messages.parent_message_id = parent_messages.id;

-- Migrate message attachments
INSERT INTO messages_files (messages_id, directus_files_id)
SELECT
    (SELECT id FROM messages WHERE subject = strapi_messages.subject LIMIT 1),
    (SELECT id FROM directus_files WHERE filename = strapi_files.name LIMIT 1)
FROM strapi_messages_attachments_links
JOIN strapi_messages ON strapi_messages_attachments_links.message_id = strapi_messages.id
JOIN strapi_files ON strapi_messages_attachments_links.file_id = strapi_files.id;
```

#### Notification Data Migration
```sql
-- Migration Script: migrate_notifications.sql
INSERT INTO notifications (
    id, title, message, type, is_read, read_at, recipient_id, sender_id,
    related_property_id, related_project_id, related_message_id,
    action_url, action_text, metadata, priority, expires_at,
    status, date_created, date_updated
)
SELECT
    gen_random_uuid(),
    title,
    message,
    type,
    is_read,
    read_at,
    (SELECT id FROM directus_users WHERE email = recipient_users.email),
    (SELECT id FROM directus_users WHERE email = sender_users.email),
    (SELECT id FROM properties WHERE slug = strapi_properties.slug),
    (SELECT id FROM projects WHERE slug = strapi_projects.slug),
    (SELECT id FROM messages WHERE subject = strapi_messages.subject LIMIT 1),
    action_url,
    action_text,
    metadata::json,
    priority,
    expires_at,
    'active',
    created_at,
    updated_at
FROM strapi_notifications
LEFT JOIN strapi_users recipient_users ON strapi_notifications.recipient_id = recipient_users.id
LEFT JOIN strapi_users sender_users ON strapi_notifications.sender_id = sender_users.id
LEFT JOIN strapi_properties ON strapi_notifications.related_property_id = strapi_properties.id
LEFT JOIN strapi_projects ON strapi_notifications.related_project_id = strapi_projects.id
LEFT JOIN strapi_messages ON strapi_notifications.related_message_id = strapi_messages.id;
```

#### Nearby Place Category Data Migration
```sql
-- Migration Script: migrate_nearby_place_categories.sql
INSERT INTO nearby_place_categories (
    id, name, display_name, description, icon, google_place_types,
    enabled, search_radius, max_results, priority, color,
    status, date_created, date_updated
)
SELECT
    gen_random_uuid(),
    name,
    display_name,
    description,
    icon,
    google_place_types::json,
    enabled,
    search_radius,
    max_results,
    priority,
    color,
    'active',
    created_at,
    updated_at
FROM strapi_nearby_place_categories;
```

---

## 3. Backend File-by-File Migration

### 3.1 Configuration Files Migration

#### Database Configuration
**Before (Strapi):** `backend/config/database.ts`
```typescript
export default ({ env }) => ({
  connection: {
    client: env('DATABASE_CLIENT', 'sqlite'),
    connection: {
      filename: path.join(__dirname, '..', '..', env('DATABASE_FILENAME', '.tmp/data.db')),
    },
    useNullAsDefault: true,
  },
});
```

**After (Directus):** `.env`
```env
# Database Configuration
DB_CLIENT="sqlite3"
DB_FILENAME="./data/database.db"

# For PostgreSQL production
# DB_CLIENT="pg"
# DB_HOST="localhost"
# DB_PORT="5432"
# DB_DATABASE="directus_real_estate"
# DB_USER="directus"
# DB_PASSWORD="password"

# Directus Configuration
KEY="your-secret-key"
SECRET="your-secret-key"

# File Storage
STORAGE_LOCATIONS="local"
STORAGE_LOCAL_DRIVER="local"
STORAGE_LOCAL_ROOT="./uploads"

# Email Configuration
EMAIL_FROM="<EMAIL>"
EMAIL_TRANSPORT="smtp"
EMAIL_SMTP_HOST="smtp.gmail.com"
EMAIL_SMTP_PORT="587"
EMAIL_SMTP_USER="<EMAIL>"
EMAIL_SMTP_PASSWORD="your-password"

# Google Maps API
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# CORS Configuration
CORS_ENABLED="true"
CORS_ORIGIN="http://localhost:3000,http://localhost:1337"
```

#### Plugin Configuration Migration
**Before (Strapi):** `backend/config/plugins.ts`
```typescript
export default () => ({
  'users-permissions': {
    config: {
      register: {
        allowedFields: ['username', 'email', 'password', 'firstName', 'lastName', 'phone', 'company'],
      },
    },
  },
  upload: {
    config: {
      sizeLimit: 10 * 1024 * 1024, // 10MB
      breakpoints: {
        xlarge: 1920,
        large: 1000,
        medium: 750,
        small: 500,
        xsmall: 64
      },
      responsiveDimensions: true,
    },
  },
  graphql: {
    config: {
      endpoint: '/graphql',
      shadowCRUD: true,
      playgroundAlways: false,
      depthLimit: 7,
      amountLimit: 100,
    },
  },
});
```

**After (Directus):** `extensions/hooks/file-upload/index.js`
```javascript
export default ({ filter, action }) => {
  // File upload size limit
  filter('files.upload', async (payload, meta, context) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (payload.size > maxSize) {
      throw new Error('File size exceeds 10MB limit');
    }
    
    return payload;
  });

  // Generate responsive images
  action('files.upload', async (meta, context) => {
    const { payload, key } = meta;
    
    if (payload.type?.startsWith('image/')) {
      // Generate responsive image variants
      await generateImageVariants(key, {
        xlarge: 1920,
        large: 1000,
        medium: 750,
        small: 500,
        xsmall: 64
      });
    }
  });
};

async function generateImageVariants(fileKey, breakpoints) {
  // Implementation for generating responsive images
  // Using sharp or similar image processing library
}
```

### 3.2 Custom Services Migration

#### ViewTracker Service Migration
**Before (Strapi):** `backend/src/api/property/services/viewTracker.ts`
```typescript
class ViewTracker {
  private viewQueue: ViewTrackingEntry[] = [];
  private viewCache: ViewCountCache = {};
  private sessionTracker: SessionTracker = {};
  
  async trackView(propertyId: string, userAgent?: string, ip?: string, userId?: string): Promise<boolean> {
    // Anti-spam protection logic
    const sessionKey = this.createSessionKey(propertyId, ip, userAgent, userId);
    const now = Date.now();
    
    const lastView = this.sessionTracker[sessionKey];
    const isSpamView = lastView && (now - lastView.lastViewTime) < this.sessionTimeout;
    
    if (isSpamView) {
      return false;
    }
    
    // Bot detection
    const isBotRequest = this.detectBotTraffic(userAgent, ip);
    if (isBotRequest) {
      return false;
    }
    
    // Queue for batch processing
    this.viewQueue.push({
      propertyId,
      timestamp: now,
      userAgent,
      ip,
      sessionId: sessionKey,
      userId
    });
    
    this.updateCache(propertyId);
    return true;
  }
}
```

**After (Directus):** `extensions/hooks/view-tracker/index.js`
```javascript
import { createHash } from 'crypto';

class DirectusViewTracker {
  constructor(database, logger) {
    this.database = database;
    this.logger = logger;
    this.viewQueue = [];
    this.viewCache = new Map();
    this.sessionTracker = new Map();
    this.batchSize = 10;
    this.sessionTimeout = 30000; // 30 seconds
    
    this.startBatchProcessor();
    this.startSessionCleanup();
  }

  async trackView(propertyId, userAgent, ip, userId) {
    try {
      const sessionKey = this.createSessionKey(propertyId, ip, userAgent, userId);
      const now = Date.now();

      // Anti-spam protection
      const lastView = this.sessionTracker.get(sessionKey);
      if (lastView && (now - lastView.lastViewTime) < this.sessionTimeout) {
        return false;
      }

      // Bot detection
      if (this.detectBotTraffic(userAgent, ip)) {
        return false;
      }

      // Update session tracker
      this.sessionTracker.set(sessionKey, {
        propertyId,
        lastViewTime: now
      });

      // Add to queue
      this.viewQueue.push({
        propertyId,
        timestamp: now,
        userAgent,
        ip,
        sessionId: sessionKey,
        userId
      });

      // Update cache
      this.updateCache(propertyId);

      // Process batch if needed
      if (this.viewQueue.length >= this.batchSize) {
        setImmediate(() => this.processBatch());
      }

      return true;
    } catch (error) {
      this.logger.error('Error tracking view:', error);
      return false;
    }
  }

  async processBatch() {
    if (this.viewQueue.length === 0) return;

    const batch = this.viewQueue.splice(0, this.batchSize);
    const viewCounts = {};

    // Aggregate views by property
    batch.forEach(entry => {
      viewCounts[entry.propertyId] = (viewCounts[entry.propertyId] || 0) + 1;
    });

    // Update database
    for (const [propertyId, increment] of Object.entries(viewCounts)) {
      try {
        await this.database('properties')
          .where('id', propertyId)
          .increment('views', increment);
          
        this.logger.info(`Updated view count for property ${propertyId}: +${increment}`);
      } catch (error) {
        this.logger.error(`Failed to update views for property ${propertyId}:`, error);
        // Re-queue failed updates
        this.viewQueue.push({ propertyId, timestamp: Date.now() });
      }
    }
  }

  createSessionKey(propertyId, ip, userAgent, userId) {
    const data = `${propertyId}-${ip}-${userAgent}-${userId || 'anonymous'}`;
    return createHash('md5').update(data).digest('hex');
  }

  detectBotTraffic(userAgent, ip) {
    if (!userAgent) return true;
    
    const botPatterns = [
      /bot/i, /crawler/i, /spider/i, /scraper/i,
      /googlebot/i, /bingbot/i, /slurp/i, /duckduckbot/i
    ];
    
    return botPatterns.some(pattern => pattern.test(userAgent));
  }

  updateCache(propertyId) {
    const cached = this.viewCache.get(propertyId) || { count: 0, pendingIncrement: 0 };
    cached.pendingIncrement += 1;
    cached.lastUpdated = Date.now();
    this.viewCache.set(propertyId, cached);
  }

  startBatchProcessor() {
    setInterval(() => {
      if (this.viewQueue.length > 0) {
        this.processBatch();
      }
    }, 5000); // Process every 5 seconds
  }

  startSessionCleanup() {
    setInterval(() => {
      const now = Date.now();
      const expiredSessions = [];
      
      for (const [key, session] of this.sessionTracker.entries()) {
        if (now - session.lastViewTime > this.sessionTimeout * 2) {
          expiredSessions.push(key);
        }
      }
      
      expiredSessions.forEach(key => this.sessionTracker.delete(key));
    }, 60000); // Cleanup every minute
  }
}

// Export as Directus hook
export default ({ database, logger }) => {
  const viewTracker = new DirectusViewTracker(database, logger);

  return {
    filter: {
      'items.read': async (payload, meta, context) => {
        // Track view when property is accessed
        if (meta.collection === 'properties' && meta.keys?.length === 1) {
          const propertyId = meta.keys[0];
          const { req } = context;
          
          await viewTracker.trackView(
            propertyId,
            req.headers['user-agent'],
            req.ip,
            context.user?.id
          );
        }
        
        return payload;
      }
    }
  };
};
```

### 3.3 Custom API Endpoints Migration

#### Property Search and Filtering
**Before (Strapi):** `backend/src/api/property/controllers/property.ts`
```typescript
export default factories.createCoreController('api::property.property', ({ strapi }) => ({
  async find(ctx) {
    const result = await fetchProperties({
      query: ctx.query,
      populationType: 'list',
      pagination: { page: 1, pageSize: 20 },
      defaultSort: { createdAt: 'desc' }
    });

    const sanitized = await this.sanitizeOutput(result.data, ctx);
    return this.transformResponse(sanitized, result.meta);
  },

  async search(ctx) {
    const { filters = {}, sort = 'createdAt:desc', pagination = {} } = ctx.query;

    // Build complex filters
    const queryFilters = {
      $and: [
        { publishedAt: { $notNull: true } },
        ...(filters.city ? [{ city: { $containsi: filters.city } }] : []),
        ...(filters.propertyType ? [{ propertyType: { $eq: filters.propertyType } }] : []),
        ...(filters.priceMin ? [{ price: { $gte: parseFloat(filters.priceMin) } }] : []),
        ...(filters.priceMax ? [{ price: { $lte: parseFloat(filters.priceMax) } }] : []),
      ]
    };

    const properties = await strapi.entityService.findMany('api::property.property', {
      filters: queryFilters,
      sort: [sort],
      pagination,
      populate: {
        images: { fields: ['url', 'alternativeText'] },
        owner: { fields: ['username', 'email'] }
      }
    });

    return { data: properties };
  }
}));
```

**After (Directus):** `extensions/endpoints/properties/index.js`
```javascript
export default {
  id: 'properties-search',
  handler: (router, { services, database, logger }) => {
    const { ItemsService } = services;

    // Property search endpoint
    router.get('/search', async (req, res) => {
      try {
        const {
          city,
          property_type,
          offer,
          price_min,
          price_max,
          bedrooms,
          bathrooms,
          features,
          page = 1,
          limit = 20,
          sort = '-date_created'
        } = req.query;

        const propertiesService = new ItemsService('properties', {
          database,
          schema: req.schema,
          accountability: req.accountability
        });

        // Build filters
        const filters = {
          status: { _eq: 'published' }
        };

        if (city) filters.city = { _icontains: city };
        if (property_type) filters.property_type = { _eq: property_type };
        if (offer) filters.offer = { _eq: offer };
        if (price_min) filters.price = { _gte: parseFloat(price_min) };
        if (price_max) {
          filters.price = filters.price || {};
          filters.price._lte = parseFloat(price_max);
        }
        if (bedrooms) filters.bedrooms = { _eq: parseInt(bedrooms) };
        if (bathrooms) filters.bathrooms = { _eq: parseInt(bathrooms) };
        if (features) {
          const featureArray = Array.isArray(features) ? features : [features];
          filters.features = { _contains: featureArray };
        }

        // Execute query
        const result = await propertiesService.readByQuery({
          filter: filters,
          sort: [sort],
          limit: parseInt(limit),
          offset: (parseInt(page) - 1) * parseInt(limit),
          fields: [
            'id', 'title', 'description', 'price', 'currency',
            'property_type', 'offer', 'bedrooms', 'bathrooms',
            'area', 'city', 'country', 'featured', 'views', 'slug',
            'date_created', 'date_updated'
          ]
        });

        // Get total count for pagination
        const totalCount = await propertiesService.readByQuery({
          filter: filters,
          aggregate: { count: '*' }
        });

        res.json({
          data: result,
          meta: {
            pagination: {
              page: parseInt(page),
              pageSize: parseInt(limit),
              total: totalCount[0].count,
              pageCount: Math.ceil(totalCount[0].count / parseInt(limit))
            }
          }
        });
      } catch (error) {
        logger.error('Property search error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Featured properties endpoint
    router.get('/featured', async (req, res) => {
      try {
        const propertiesService = new ItemsService('properties', {
          database,
          schema: req.schema,
          accountability: req.accountability
        });

        const featured = await propertiesService.readByQuery({
          filter: {
            status: { _eq: 'published' },
            featured: { _eq: true }
          },
          sort: ['-date_created'],
          limit: 8,
          fields: [
            'id', 'title', 'price', 'currency', 'property_type',
            'offer', 'bedrooms', 'bathrooms', 'area', 'city',
            'country', 'slug', 'views'
          ]
        });

        res.json({ data: featured });
      } catch (error) {
        logger.error('Featured properties error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Property by slug endpoint
    router.get('/by-slug/:slug', async (req, res) => {
      try {
        const { slug } = req.params;

        const propertiesService = new ItemsService('properties', {
          database,
          schema: req.schema,
          accountability: req.accountability
        });

        const properties = await propertiesService.readByQuery({
          filter: {
            slug: { _eq: slug },
            status: { _eq: 'published' }
          },
          limit: 1
        });

        if (properties.length === 0) {
          return res.status(404).json({ error: 'Property not found' });
        }

        res.json({ data: properties[0] });
      } catch (error) {
        logger.error('Property by slug error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });
  }
};
```

#### Message and Notification Endpoints
**After (Directus):** `extensions/endpoints/messaging/index.js`
```javascript
export default {
  id: 'messaging-system',
  handler: (router, { services, database, logger }) => {
    const { ItemsService } = services;

    // Get user's inbox
    router.get('/messages/inbox', async (req, res) => {
      try {
        if (!req.accountability?.user) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const userId = req.accountability.user;
        const { page = 1, limit = 20, unreadOnly = false } = req.query;

        const messagesService = new ItemsService('messages', {
          database,
          schema: req.schema,
          accountability: req.accountability
        });

        const filters = {
          recipient_id: { _eq: userId }
        };

        if (unreadOnly === 'true') {
          filters.is_read = { _eq: false };
        }

        const messages = await messagesService.readByQuery({
          filter: filters,
          sort: ['-date_created'],
          limit: parseInt(limit),
          offset: (parseInt(page) - 1) * parseInt(limit),
          fields: [
            'id', 'subject', 'content', 'message_type', 'is_read',
            'read_at', 'date_created',
            { sender_id: ['id', 'first_name', 'last_name', 'email'] },
            { property_id: ['id', 'title', 'slug'] },
            { project_id: ['id', 'title', 'slug'] }
          ]
        });

        res.json({ data: messages });
      } catch (error) {
        logger.error('Inbox error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Get user's sent messages
    router.get('/messages/sent', async (req, res) => {
      try {
        if (!req.accountability?.user) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const userId = req.accountability.user;
        const { page = 1, limit = 20 } = req.query;

        const messagesService = new ItemsService('messages', {
          database,
          schema: req.schema,
          accountability: req.accountability
        });

        const messages = await messagesService.readByQuery({
          filter: { sender_id: { _eq: userId } },
          sort: ['-date_created'],
          limit: parseInt(limit),
          offset: (parseInt(page) - 1) * parseInt(limit),
          fields: [
            'id', 'subject', 'content', 'message_type', 'is_read',
            'read_at', 'date_created',
            { recipient_id: ['id', 'first_name', 'last_name', 'email'] },
            { property_id: ['id', 'title', 'slug'] },
            { project_id: ['id', 'title', 'slug'] }
          ]
        });

        res.json({ data: messages });
      } catch (error) {
        logger.error('Sent messages error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Mark message as read
    router.put('/messages/:id/mark-as-read', async (req, res) => {
      try {
        if (!req.accountability?.user) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const { id } = req.params;
        const userId = req.accountability.user;

        const messagesService = new ItemsService('messages', {
          database,
          schema: req.schema,
          accountability: req.accountability
        });

        // Verify user is recipient
        const message = await messagesService.readOne(id, {
          fields: ['recipient_id']
        });

        if (message.recipient_id !== userId) {
          return res.status(403).json({ error: 'Access denied' });
        }

        const updatedMessage = await messagesService.updateOne(id, {
          is_read: true,
          read_at: new Date().toISOString()
        });

        res.json({ data: updatedMessage });
      } catch (error) {
        logger.error('Mark as read error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Get user's notifications
    router.get('/notifications', async (req, res) => {
      try {
        if (!req.accountability?.user) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const userId = req.accountability.user;
        const { page = 1, limit = 20, unreadOnly = false } = req.query;

        const notificationsService = new ItemsService('notifications', {
          database,
          schema: req.schema,
          accountability: req.accountability
        });

        const filters = {
          recipient_id: { _eq: userId },
          _or: [
            { expires_at: { _null: true } },
            { expires_at: { _gt: new Date().toISOString() } }
          ]
        };

        if (unreadOnly === 'true') {
          filters.is_read = { _eq: false };
        }

        const notifications = await notificationsService.readByQuery({
          filter: filters,
          sort: ['-date_created'],
          limit: parseInt(limit),
          offset: (parseInt(page) - 1) * parseInt(limit),
          fields: [
            'id', 'title', 'message', 'type', 'priority', 'is_read',
            'read_at', 'action_url', 'action_text', 'date_created',
            { sender_id: ['id', 'first_name', 'last_name'] }
          ]
        });

        res.json({ data: notifications });
      } catch (error) {
        logger.error('Notifications error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Mark notification as read
    router.put('/notifications/:id/mark-as-read', async (req, res) => {
      try {
        if (!req.accountability?.user) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const { id } = req.params;
        const userId = req.accountability.user;

        const notificationsService = new ItemsService('notifications', {
          database,
          schema: req.schema,
          accountability: req.accountability
        });

        // Verify user is recipient
        const notification = await notificationsService.readOne(id, {
          fields: ['recipient_id']
        });

        if (notification.recipient_id !== userId) {
          return res.status(403).json({ error: 'Access denied' });
        }

        const updatedNotification = await notificationsService.updateOne(id, {
          is_read: true,
          read_at: new Date().toISOString()
        });

        res.json({ data: updatedNotification });
      } catch (error) {
        logger.error('Mark notification as read error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Get unread count
    router.get('/notifications/unread-count', async (req, res) => {
      try {
        if (!req.accountability?.user) {
          return res.status(401).json({ error: 'Authentication required' });
        }

        const userId = req.accountability.user;

        const count = await database('notifications')
          .where('recipient_id', userId)
          .where('is_read', false)
          .where(function() {
            this.whereNull('expires_at')
              .orWhere('expires_at', '>', new Date().toISOString());
          })
          .count('* as count')
          .first();

        res.json({ data: { count: count.count } });
      } catch (error) {
        logger.error('Unread count error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });
  }
};
```

#### Nearby Place Categories Endpoints
**After (Directus):** `extensions/endpoints/nearby-places/index.js`
```javascript
export default {
  id: 'nearby-place-categories',
  handler: (router, { services, database, logger }) => {
    const { ItemsService } = services;

    // Get enabled categories
    router.get('/nearby-place-categories/enabled', async (req, res) => {
      try {
        const categoriesService = new ItemsService('nearby_place_categories', {
          database,
          schema: req.schema,
          accountability: req.accountability
        });

        const categories = await categoriesService.readByQuery({
          filter: { enabled: { _eq: true } },
          sort: ['-priority', 'display_name'],
          fields: [
            'id', 'name', 'display_name', 'description', 'icon',
            'google_place_types', 'search_radius', 'max_results',
            'priority', 'color'
          ]
        });

        res.json({ data: categories });
      } catch (error) {
        logger.error('Enabled categories error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Get Google place types
    router.get('/nearby-place-categories/google-place-types', async (req, res) => {
      try {
        const googlePlaceTypes = [
          'restaurant', 'cafe', 'school', 'hospital', 'pharmacy',
          'bank', 'atm', 'gas_station', 'shopping_mall', 'supermarket',
          'gym', 'park', 'library', 'museum', 'movie_theater',
          'bus_station', 'subway_station', 'airport', 'taxi_stand'
        ];

        res.json({ data: googlePlaceTypes });
      } catch (error) {
        logger.error('Google place types error:', error);
        res.status(500).json({ error: 'Internal server error' });
      }
    });
  }
};
```

---

## 4. Frontend Integration Changes

### 4.1 API Client Migration

#### Authentication Changes
**Before (Strapi):** `frontend/src/lib/api.ts`
```typescript
import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337';

export const api = axios.create({
  baseURL: `${API_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('jwt');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Authentication API
export const authAPI = {
  login: async (identifier: string, password: string) => {
    const response = await api.post('/auth/local', {
      identifier,
      password,
    });
    return response.data;
  },

  register: async (username: string, email: string, password: string) => {
    const response = await api.post('/auth/local/register', {
      username,
      email,
      password,
    });
    return response.data;
  },

  me: async () => {
    const response = await api.get('/users/me');
    return response.data;
  },
};
```

**After (Directus):** `frontend/src/lib/directus.ts`
```typescript
import { createDirectus, rest, authentication, readItems, createItem, updateItem, deleteItem } from '@directus/sdk';

const DIRECTUS_URL = process.env.NEXT_PUBLIC_DIRECTUS_URL || 'http://localhost:8055';

// Create Directus client
export const directus = createDirectus(DIRECTUS_URL)
  .with(rest())
  .with(authentication('json'));

// Authentication API
export const authAPI = {
  login: async (email: string, password: string) => {
    try {
      const result = await directus.login(email, password);

      // Store tokens
      localStorage.setItem('directus_access_token', result.access_token);
      localStorage.setItem('directus_refresh_token', result.refresh_token);

      return result;
    } catch (error) {
      throw new Error('Login failed');
    }
  },

  register: async (userData: {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
  }) => {
    try {
      const user = await directus.request(
        createItem('directus_users', {
          ...userData,
          status: 'active',
          role: await getPublicRole()
        })
      );

      // Auto-login after registration
      return await authAPI.login(userData.email, userData.password);
    } catch (error) {
      throw new Error('Registration failed');
    }
  },

  me: async () => {
    try {
      const user = await directus.request(
        readItems('directus_users', {
          filter: { id: { _eq: '$CURRENT_USER' } },
          fields: [
            'id', 'first_name', 'last_name', 'email',
            'phone', 'company', 'job_title', 'bio',
            'is_agent', 'membership_id'
          ]
        })
      );
      return user[0];
    } catch (error) {
      throw new Error('Failed to fetch user data');
    }
  },

  logout: async () => {
    try {
      await directus.logout();
      localStorage.removeItem('directus_access_token');
      localStorage.removeItem('directus_refresh_token');
    } catch (error) {
      // Silent fail for logout
    }
  },

  refreshToken: async () => {
    try {
      const refreshToken = localStorage.getItem('directus_refresh_token');
      if (!refreshToken) throw new Error('No refresh token');

      const result = await directus.refresh();
      localStorage.setItem('directus_access_token', result.access_token);

      return result;
    } catch (error) {
      // Clear tokens and redirect to login
      localStorage.removeItem('directus_access_token');
      localStorage.removeItem('directus_refresh_token');
      throw error;
    }
  }
};

// Helper function to get public role ID
async function getPublicRole() {
  const roles = await directus.request(
    readItems('directus_roles', {
      filter: { name: { _eq: 'Public' } },
      fields: ['id']
    })
  );
  return roles[0]?.id;
}

// Initialize authentication on app start
export const initializeAuth = async () => {
  const token = localStorage.getItem('directus_access_token');
  if (token) {
    try {
      await directus.setToken(token);
      // Verify token is still valid
      await authAPI.me();
    } catch (error) {
      // Token invalid, try to refresh
      try {
        await authAPI.refreshToken();
      } catch (refreshError) {
        // Refresh failed, clear tokens
        localStorage.removeItem('directus_access_token');
        localStorage.removeItem('directus_refresh_token');
      }
    }
  }
};
```

#### Properties API Migration
**Before (Strapi):** `frontend/src/lib/api.ts`
```typescript
export const propertiesAPI = {
  search: async (filters: any) => {
    const response = await api.get('/properties', {
      params: {
        populate: ['images', 'owner'],
        filters,
        sort: ['createdAt:desc'],
        pagination: { page: 1, pageSize: 20 }
      }
    });
    return response.data;
  },

  getById: async (id: string) => {
    const response = await api.get(`/properties/${id}`, {
      params: {
        populate: ['images', 'owner', 'agent', 'project']
      }
    });
    return response.data;
  },

  create: async (propertyData: any) => {
    const response = await api.post('/properties', { data: propertyData });
    return response.data;
  },

  update: async (id: string, propertyData: any) => {
    const response = await api.put(`/properties/${id}`, { data: propertyData });
    return response.data;
  },

  delete: async (id: string) => {
    const response = await api.delete(`/properties/${id}`);
    return response.data;
  }
};
```

**After (Directus):** `frontend/src/lib/directus.ts`
```typescript
export const propertiesAPI = {
  search: async (filters: any) => {
    try {
      const {
        city,
        property_type,
        offer,
        price_min,
        price_max,
        bedrooms,
        bathrooms,
        features,
        page = 1,
        pageSize = 20,
        sort = '-date_created'
      } = filters;

      // Build Directus filter object
      const directusFilters: any = {
        status: { _eq: 'published' }
      };

      if (city) directusFilters.city = { _icontains: city };
      if (property_type) directusFilters.property_type = { _eq: property_type };
      if (offer) directusFilters.offer = { _eq: offer };
      if (price_min) directusFilters.price = { _gte: parseFloat(price_min) };
      if (price_max) {
        directusFilters.price = directusFilters.price || {};
        directusFilters.price._lte = parseFloat(price_max);
      }
      if (bedrooms) directusFilters.bedrooms = { _eq: parseInt(bedrooms) };
      if (bathrooms) directusFilters.bathrooms = { _eq: parseInt(bathrooms) };
      if (features && features.length > 0) {
        directusFilters.features = { _contains: features };
      }

      const result = await directus.request(
        readItems('properties', {
          filter: directusFilters,
          sort: [sort],
          limit: pageSize,
          offset: (page - 1) * pageSize,
          fields: [
            'id', 'title', 'description', 'price', 'currency',
            'property_type', 'offer', 'bedrooms', 'bathrooms',
            'area', 'area_unit', 'city', 'country', 'featured',
            'views', 'slug', 'date_created', 'date_updated',
            { owner_id: ['id', 'first_name', 'last_name', 'email'] }
          ]
        })
      );

      // Get total count for pagination
      const totalCount = await directus.request(
        readItems('properties', {
          filter: directusFilters,
          aggregate: { count: '*' }
        })
      );

      return {
        data: result,
        meta: {
          pagination: {
            page,
            pageSize,
            total: totalCount[0].count,
            pageCount: Math.ceil(totalCount[0].count / pageSize)
          }
        }
      };
    } catch (error) {
      console.error('Property search error:', error);
      throw error;
    }
  },

  getById: async (id: string) => {
    try {
      const result = await directus.request(
        readItems('properties', {
          filter: { id: { _eq: id } },
          fields: [
            '*',
            { owner_id: ['id', 'first_name', 'last_name', 'email', 'phone'] },
            { agent_id: ['id', 'first_name', 'last_name', 'email', 'phone'] },
            { project_id: ['id', 'title', 'slug'] }
          ]
        })
      );

      return { data: result[0] };
    } catch (error) {
      console.error('Property fetch error:', error);
      throw error;
    }
  },

  create: async (propertyData: any) => {
    try {
      const result = await directus.request(
        createItem('properties', {
          ...propertyData,
          status: 'draft',
          date_created: new Date().toISOString(),
          date_updated: new Date().toISOString()
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Property creation error:', error);
      throw error;
    }
  },

  update: async (id: string, propertyData: any) => {
    try {
      const result = await directus.request(
        updateItem('properties', id, {
          ...propertyData,
          date_updated: new Date().toISOString()
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Property update error:', error);
      throw error;
    }
  },

  delete: async (id: string) => {
    try {
      await directus.request(deleteItem('properties', id));
      return { success: true };
    } catch (error) {
      console.error('Property deletion error:', error);
      throw error;
    }
  },

  getFeatured: async () => {
    try {
      const result = await directus.request(
        readItems('properties', {
          filter: {
            status: { _eq: 'published' },
            featured: { _eq: true }
          },
          sort: ['-date_created'],
          limit: 8,
          fields: [
            'id', 'title', 'price', 'currency', 'property_type',
            'offer', 'bedrooms', 'bathrooms', 'area', 'city',
            'country', 'slug', 'views'
          ]
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Featured properties error:', error);
      throw error;
    }
  }
};

// Messages API
export const messagesAPI = {
  getInbox: async (page = 1, limit = 20, unreadOnly = false) => {
    try {
      const result = await directus.request(
        readItems('messages', {
          filter: {
            recipient_id: { _eq: '$CURRENT_USER' },
            ...(unreadOnly ? { is_read: { _eq: false } } : {})
          },
          sort: ['-date_created'],
          limit,
          offset: (page - 1) * limit,
          fields: [
            'id', 'subject', 'content', 'message_type', 'is_read',
            'read_at', 'date_created',
            { sender_id: ['id', 'first_name', 'last_name', 'email'] },
            { property_id: ['id', 'title', 'slug'] },
            { project_id: ['id', 'title', 'slug'] }
          ]
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Inbox fetch error:', error);
      throw error;
    }
  },

  getSent: async (page = 1, limit = 20) => {
    try {
      const result = await directus.request(
        readItems('messages', {
          filter: { sender_id: { _eq: '$CURRENT_USER' } },
          sort: ['-date_created'],
          limit,
          offset: (page - 1) * limit,
          fields: [
            'id', 'subject', 'content', 'message_type', 'is_read',
            'read_at', 'date_created',
            { recipient_id: ['id', 'first_name', 'last_name', 'email'] },
            { property_id: ['id', 'title', 'slug'] },
            { project_id: ['id', 'title', 'slug'] }
          ]
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Sent messages fetch error:', error);
      throw error;
    }
  },

  create: async (messageData: any) => {
    try {
      const result = await directus.request(
        createItem('messages', {
          ...messageData,
          date_created: new Date().toISOString(),
          date_updated: new Date().toISOString()
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Message creation error:', error);
      throw error;
    }
  },

  markAsRead: async (id: string) => {
    try {
      const result = await directus.request(
        updateItem('messages', id, {
          is_read: true,
          read_at: new Date().toISOString()
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Mark as read error:', error);
      throw error;
    }
  }
};

// Notifications API
export const notificationsAPI = {
  getAll: async (page = 1, limit = 20, unreadOnly = false) => {
    try {
      const result = await directus.request(
        readItems('notifications', {
          filter: {
            recipient_id: { _eq: '$CURRENT_USER' },
            _or: [
              { expires_at: { _null: true } },
              { expires_at: { _gt: new Date().toISOString() } }
            ],
            ...(unreadOnly ? { is_read: { _eq: false } } : {})
          },
          sort: ['-date_created'],
          limit,
          offset: (page - 1) * limit,
          fields: [
            'id', 'title', 'message', 'type', 'priority', 'is_read',
            'read_at', 'action_url', 'action_text', 'date_created',
            { sender_id: ['id', 'first_name', 'last_name'] }
          ]
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Notifications fetch error:', error);
      throw error;
    }
  },

  getUnreadCount: async () => {
    try {
      const result = await directus.request(
        readItems('notifications', {
          filter: {
            recipient_id: { _eq: '$CURRENT_USER' },
            is_read: { _eq: false },
            _or: [
              { expires_at: { _null: true } },
              { expires_at: { _gt: new Date().toISOString() } }
            ]
          },
          aggregate: { count: '*' }
        })
      );

      return { data: { count: result[0].count } };
    } catch (error) {
      console.error('Unread count error:', error);
      throw error;
    }
  },

  markAsRead: async (id: string) => {
    try {
      const result = await directus.request(
        updateItem('notifications', id, {
          is_read: true,
          read_at: new Date().toISOString()
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Mark notification as read error:', error);
      throw error;
    }
  },

  markAllAsRead: async () => {
    try {
      // Get all unread notifications for current user
      const unreadNotifications = await directus.request(
        readItems('notifications', {
          filter: {
            recipient_id: { _eq: '$CURRENT_USER' },
            is_read: { _eq: false }
          },
          fields: ['id']
        })
      );

      // Mark all as read
      const updatePromises = unreadNotifications.map(notification =>
        directus.request(
          updateItem('notifications', notification.id, {
            is_read: true,
            read_at: new Date().toISOString()
          })
        )
      );

      await Promise.all(updatePromises);
      return { success: true };
    } catch (error) {
      console.error('Mark all as read error:', error);
      throw error;
    }
  }
};

// Nearby Place Categories API
export const nearbyPlaceCategoriesAPI = {
  getEnabled: async () => {
    try {
      const result = await directus.request(
        readItems('nearby_place_categories', {
          filter: { enabled: { _eq: true } },
          sort: ['-priority', 'display_name'],
          fields: [
            'id', 'name', 'display_name', 'description', 'icon',
            'google_place_types', 'search_radius', 'max_results',
            'priority', 'color'
          ]
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Enabled categories fetch error:', error);
      throw error;
    }
  },

  getAll: async () => {
    try {
      const result = await directus.request(
        readItems('nearby_place_categories', {
          sort: ['-priority', 'display_name']
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Categories fetch error:', error);
      throw error;
    }
  },

  create: async (categoryData: any) => {
    try {
      const result = await directus.request(
        createItem('nearby_place_categories', {
          ...categoryData,
          date_created: new Date().toISOString(),
          date_updated: new Date().toISOString()
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Category creation error:', error);
      throw error;
    }
  },

  update: async (id: string, categoryData: any) => {
    try {
      const result = await directus.request(
        updateItem('nearby_place_categories', id, {
          ...categoryData,
          date_updated: new Date().toISOString()
        })
      );

      return { data: result };
    } catch (error) {
      console.error('Category update error:', error);
      throw error;
    }
  }
};
```

### 4.2 React Hooks Migration

#### useProperties Hook
**Before (Strapi):** `frontend/src/hooks/useProperties.ts`
```typescript
import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { propertiesAPI } from '@/lib/api';

export const useProperties = (filters?: any) => {
  return useQuery({
    queryKey: ['properties', filters],
    queryFn: () => propertiesAPI.search(filters || {}),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useProperty = (id: string, enabled = true) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: () => propertiesAPI.getById(id),
    enabled: !!id && enabled,
    staleTime: 10 * 60 * 1000,
  });
};
```

**After (Directus):** `frontend/src/hooks/useProperties.ts`
```typescript
import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { propertiesAPI } from '@/lib/directus';

export const useProperties = (filters?: any) => {
  return useQuery({
    queryKey: ['properties', filters],
    queryFn: () => propertiesAPI.search(filters || {}),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

export const useProperty = (id: string, enabled = true) => {
  return useQuery({
    queryKey: ['property', id],
    queryFn: () => propertiesAPI.getById(id),
    enabled: !!id && enabled,
    staleTime: 10 * 60 * 1000,
  });
};

export const useInfiniteProperties = (filters?: any) => {
  return useInfiniteQuery({
    queryKey: ['properties', 'infinite', filters],
    queryFn: ({ pageParam = 1 }) =>
      propertiesAPI.search({ ...filters, page: pageParam, pageSize: 12 }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      const { page, pageCount } = lastPage.meta?.pagination || {};
      return page < pageCount ? page + 1 : undefined;
    },
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: propertiesAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });
};

export const useUpdateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      propertiesAPI.update(id, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['property', variables.id] });
    },
  });
};

export const useDeleteProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: propertiesAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });
};

// Message hooks
export const useMessages = (type: 'inbox' | 'sent', page = 1, unreadOnly = false) => {
  return useQuery({
    queryKey: ['messages', type, page, unreadOnly],
    queryFn: () => {
      if (type === 'inbox') {
        return messagesAPI.getInbox(page, 20, unreadOnly);
      } else {
        return messagesAPI.getSent(page, 20);
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useCreateMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: messagesAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages'] });
    },
  });
};

export const useMarkMessageAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: messagesAPI.markAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['messages'] });
    },
  });
};

// Notification hooks
export const useNotifications = (page = 1, unreadOnly = false) => {
  return useQuery({
    queryKey: ['notifications', page, unreadOnly],
    queryFn: () => notificationsAPI.getAll(page, 20, unreadOnly),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useUnreadNotificationCount = () => {
  return useQuery({
    queryKey: ['notifications', 'unread-count'],
    queryFn: notificationsAPI.getUnreadCount,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
  });
};

export const useMarkNotificationAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: notificationsAPI.markAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
};

export const useMarkAllNotificationsAsRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: notificationsAPI.markAllAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
};

// Nearby Place Category hooks
export const useNearbyPlaceCategories = (enabledOnly = false) => {
  return useQuery({
    queryKey: ['nearby-place-categories', enabledOnly],
    queryFn: () => {
      if (enabledOnly) {
        return nearbyPlaceCategoriesAPI.getEnabled();
      } else {
        return nearbyPlaceCategoriesAPI.getAll();
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useCreateNearbyPlaceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: nearbyPlaceCategoriesAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nearby-place-categories'] });
    },
  });
};

export const useUpdateNearbyPlaceCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) =>
      nearbyPlaceCategoriesAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nearby-place-categories'] });
    },
  });
};
```

### 4.3 Authentication Context Migration

**Before (Strapi):** `frontend/src/contexts/AuthContext.tsx`
```typescript
import React, { createContext, useContext, useEffect, useState } from 'react';
import { authAPI } from '@/lib/api';

interface User {
  id: number;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  company?: string;
  isAgent: boolean;
  role: {
    id: number;
    name: string;
    type: string;
  };
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (identifier: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (userData: Partial<User>) => void;
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('jwt');
      const savedUser = localStorage.getItem('user');

      if (token && savedUser) {
        try {
          setUser(JSON.parse(savedUser));
          const userData = await authAPI.me();
          setUser(userData);
          localStorage.setItem('user', JSON.stringify(userData));
        } catch (error) {
          localStorage.removeItem('jwt');
          localStorage.removeItem('user');
          setUser(null);
        }
      }
      setLoading(false);
    };

    initAuth();
  }, []);

  const login = async (identifier: string, password: string) => {
    const response = await authAPI.login(identifier, password);
    const { jwt, user: userData } = response;

    localStorage.setItem('jwt', jwt);
    localStorage.setItem('user', JSON.stringify(userData));
    setUser(userData);
  };

  const logout = () => {
    localStorage.removeItem('jwt');
    localStorage.removeItem('user');
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, loading, isAuthenticated: !!user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
```

**After (Directus):** `frontend/src/contexts/AuthContext.tsx`
```typescript
import React, { createContext, useContext, useEffect, useState } from 'react';
import { authAPI, initializeAuth } from '@/lib/directus';

interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  company?: string;
  job_title?: string;
  bio?: string;
  is_agent: boolean;
  membership_id?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

interface RegisterData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  phone?: string;
  company?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        await initializeAuth();
        const userData = await authAPI.me();
        setUser(userData);
      } catch (error) {
        console.error('Auth initialization failed:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const response = await authAPI.login(email, password);
      const userData = await authAPI.me();
      setUser(userData);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      await authAPI.register(userData);
      const user = await authAPI.me();
      setUser(user);
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
      // Still clear user state even if logout request fails
      setUser(null);
    }
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
    }
  };

  const value = {
    user,
    loading,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    updateUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
```

---

## 5. Step-by-Step Implementation Guide

### Phase 1: Environment Setup (Week 1)

#### 5.1 Directus Installation
```bash
# Install Directus
npm create directus-project@latest real-estate-directus

# Navigate to project
cd real-estate-directus

# Install dependencies
npm install

# Install additional packages for extensions
npm install @directus/sdk axios sharp crypto
```

#### 5.2 Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Configure database and basic settings
# Edit .env file with your configuration
```

#### 5.3 Database Setup
```bash
# Run initial migration
npx directus database migrate:latest

# Create admin user
npx directus users create --email <EMAIL> --password admin123 --role administrator
```

### Phase 2: Schema Migration (Week 1-2)

#### 5.4 Create Collections
```bash
# Create migration files
mkdir -p migrations
touch migrations/001_create_properties.sql
touch migrations/002_create_projects.sql
touch migrations/003_create_memberships.sql
touch migrations/004_extend_users.sql

# Run migrations
npx directus database migrate:latest
```

#### 5.5 Data Migration
```bash
# Export data from Strapi
node scripts/export-strapi-data.js

# Import data to Directus
node scripts/import-to-directus.js
```

### Phase 3: Backend Extensions (Week 3-4)

#### 5.6 Create Extensions Structure
```bash
mkdir -p extensions/hooks/view-tracker
mkdir -p extensions/endpoints/properties
mkdir -p extensions/endpoints/analytics
mkdir -p extensions/hooks/file-upload
```

#### 5.7 Implement Custom Logic
- Copy ViewTracker service logic to Directus hook
- Implement property search endpoints
- Create analytics endpoints
- Set up file upload handling

### Phase 4: Frontend Migration (Week 5-7)

#### 5.8 Update API Integration
- Replace Strapi API calls with Directus SDK
- Update authentication flow
- Modify data structures
- Update React hooks

#### 5.9 Test Integration
- Test all API endpoints
- Verify authentication flow
- Check data consistency
- Performance testing

### Phase 5: Testing & Optimization (Week 8-10)

#### 5.10 Comprehensive Testing
```bash
# Run frontend tests
npm run test

# Run backend tests
npm run test:backend

# Performance testing
npm run test:performance
```

#### 5.11 Performance Optimization
- Database indexing
- Query optimization
- Caching implementation
- CDN setup

#### 5.12 Production Deployment
```bash
# Build frontend
npm run build

# Deploy Directus
docker-compose up -d

# Deploy frontend
npm run deploy
```

---

## 6. Migration Checklist

### Pre-Migration
- [ ] Backup all Strapi data
- [ ] Document current API endpoints
- [ ] Test current functionality
- [ ] Set up staging environment

### Database Migration
- [ ] Create Directus collections
- [ ] Migrate user data
- [ ] Migrate property data
- [ ] Migrate project data
- [ ] Migrate membership data
- [ ] Migrate message data
- [ ] Migrate notification data
- [ ] Migrate nearby place category data
- [ ] Verify data integrity

### Backend Migration
- [ ] Implement ViewTracker hook
- [ ] Create property search endpoints
- [ ] Implement analytics endpoints
- [ ] Create messaging system endpoints
- [ ] Implement notification system endpoints
- [ ] Create nearby place category endpoints
- [ ] Set up file upload handling
- [ ] Configure authentication
- [ ] Test all endpoints

### Frontend Migration
- [ ] Update API client
- [ ] Migrate authentication context
- [ ] Update React hooks
- [ ] Modify components
- [ ] Test user flows
- [ ] Performance testing

### Post-Migration
- [ ] Monitor system performance
- [ ] Verify all features work
- [ ] Update documentation
- [ ] Train team on new system
- [ ] Plan rollback strategy

---

## 7. Rollback Strategy

### Emergency Rollback
1. **DNS Switch**: Point domain back to Strapi instance
2. **Database Restore**: Restore Strapi database from backup
3. **Frontend Deployment**: Deploy previous Strapi-compatible frontend
4. **Monitoring**: Verify all systems operational

### Gradual Rollback
1. **Feature Flags**: Disable Directus features gradually
2. **API Gateway**: Route traffic back to Strapi endpoints
3. **Data Sync**: Sync any new data back to Strapi
4. **Complete Switch**: Full rollback when ready

This comprehensive migration guide provides a complete roadmap for migrating from Strapi v5 to Directus CMS while preserving all custom functionality and optimizations.
