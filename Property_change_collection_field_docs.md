# Strapi Collection Field Conversion Guide: JSON to Enum

## 📋 Overview

This comprehensive guide provides a standardized workflow for converting any JSON field to enum type in Strapi v5 collections. This process ensures data integrity, maintains frontend-backend synchronization, and provides a safe migration path.

## 🎯 When to Convert JSON to Enum

### Use Enum When:
- ✅ **Fixed set of values** (e.g., property types, statuses, categories)
- ✅ **Predefined options** that rarely change
- ✅ **Better database performance** needed for filtering/indexing
- ✅ **Data validation** is critical
- ✅ **Multiple selection** from known options

### Keep JSON When:
- ❌ **Dynamic values** that users can add
- ❌ **Complex nested data** structures
- ❌ **Frequently changing** option sets
- ❌ **User-generated content** without restrictions

## 🗂️ File Identification Matrix

| Component Type | File Path Pattern | Purpose | Required Changes |
|---|---|---|---|
| **Backend Schema** | `backend/src/api/[collection]/content-types/[collection]/schema.json` | Field definition | Add enum field, keep JSON temporarily |
| **Backend Controller** | `backend/src/api/[collection]/controllers/[collection].ts` | API logic | Add enum endpoint, update filtering |
| **Backend Routes** | `backend/src/api/[collection]/routes/[collection].ts` | API endpoints | Add enum values endpoint |
| **Frontend Types** | `frontend/src/types/api.ts` | TypeScript definitions | Update interface with enum field |
| **Edit Forms** | `frontend/src/app/dashboard/[collection]/[id]/edit/page.tsx` | Edit functionality | Replace hardcoded options with API calls |
| **Create Forms** | `frontend/src/app/submit-[collection]/page.tsx` | Create functionality | Replace hardcoded options with API calls |
| **Filter Components** | `frontend/src/components/Filters/[Field]Filter.tsx` | Search/filtering | Update to use enum values |
| **API Client** | `frontend/src/lib/api.ts` | API integration | Add enum fetching methods |
| **Migration Script** | `scripts/migrate-[field]-to-enum.ps1` | Data migration | Convert existing JSON data |

## 🔄 General Process Overview

### Phase 1: Preparation & Schema Update
1. **Backup database** and codebase
2. **Add new enum field** alongside existing JSON field
3. **Update backend schema** with enum definition
4. **Create enum values endpoint** in controller
5. **Test schema changes** in development

### Phase 2: Data Migration
1. **Create migration script** to convert JSON to enum
2. **Run migration** in development environment
3. **Validate data integrity** after migration
4. **Test API endpoints** with new enum field

### Phase 3: Frontend Updates
1. **Update TypeScript types** to include enum field
2. **Replace hardcoded options** with dynamic API calls
3. **Update form components** to use new enum field
4. **Update filter components** if applicable
5. **Test all frontend functionality**

### Phase 4: Production Deployment
1. **Deploy backend changes** first
2. **Run migration script** on production data
3. **Deploy frontend changes**
4. **Monitor for issues** and validate functionality
5. **Remove old JSON field** after validation period

## 📝 Step-by-Step Implementation

### Step 1: Backend Schema Update

**Template for any collection:**

```json
// backend/src/api/[COLLECTION]/content-types/[COLLECTION]/schema.json
{
  "attributes": {
    // Keep existing JSON field temporarily
    "[FIELD_NAME]": {
      "type": "json"
    },
    // Add new enum field
    "[FIELD_NAME]Enum": {
      "type": "enumeration",
      "multiple": true,  // Set to false for single selection
      "enum": [
        "option-1",
        "option-2",
        "option-3"
        // Add all possible enum values
      ],
      "required": false  // Set based on your needs
    }
  }
}
```

**Real Example - Property Features:**

```json
// backend/src/api/property/content-types/property/schema.json
{
  "attributes": {
    "features": {
      "type": "json"
    },
    "featuresEnum": {
      "type": "enumeration",
      "multiple": true,
      "enum": [
        "swimming-pool",
        "gym",
        "garden",
        "balcony",
        "terrace",
        "garage",
        "security",
        "elevator",
        "air-conditioning",
        "heating",
        "fireplace",
        "walk-in-closet",
        "storage-room",
        "laundry-room",
        "maid-room",
        "wifi",
        "view",
        "concierge",
        "shopping",
        "cafe",
        "parking"
      ]
    }
  }
}
```

### Step 2: Backend Controller Enhancement

**Template for any collection:**

```typescript
// backend/src/api/[COLLECTION]/controllers/[COLLECTION].ts

export default createCoreController('api::[COLLECTION].[COLLECTION]', ({ strapi }) => ({
  // Add enum values endpoint
  async get[FieldName]Options(ctx) {
    try {
      const schema = strapi.getModel('api::[COLLECTION].[COLLECTION]');
      const enumField = schema.attributes.[FIELD_NAME]Enum;
      
      if (enumField && enumField.enum) {
        const options = enumField.enum.map(value => ({
          value,
          label: this.formatEnumLabel(value)
        }));
        
        return ctx.send({ data: options });
      }
      
      return ctx.send({ data: [] });
    } catch (error) {
      console.error(`Error fetching ${[FIELD_NAME]} options:`, error);
      return ctx.internalServerError(`Failed to fetch ${[FIELD_NAME]} options`);
    }
  },

  // Helper method to format enum values to display labels
  formatEnumLabel(value) {
    return value
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  },

  // Update find method to support enum filtering
  async find(ctx) {
    try {
      const { query } = ctx;
      const filters = query.filters || {};
      
      // Handle enum filtering
      if (query.[FIELD_NAME]Enum) {
        filters.[FIELD_NAME]Enum = {
          $in: Array.isArray(query.[FIELD_NAME]Enum) 
            ? query.[FIELD_NAME]Enum 
            : [query.[FIELD_NAME]Enum]
        };
      }
      
      const result = await super.find({ ...ctx, query: { ...query, filters } });
      return result;
    } catch (error) {
      console.error(`Error in ${[COLLECTION]} find:`, error);
      return ctx.internalServerError(`Failed to fetch ${[COLLECTION]}s`);
    }
  }
}));
```

**Real Example - Property Features:**

```typescript
// backend/src/api/property/controllers/property.ts

async getFeaturesOptions(ctx) {
  try {
    const schema = strapi.getModel('api::property.property');
    const featuresField = schema.attributes.featuresEnum;
    
    if (featuresField && featuresField.enum) {
      const features = featuresField.enum.map(value => ({
        value,
        label: value.split('-').map(word => 
          word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ')
      }));
      
      return ctx.send({ data: features });
    }
    
    return ctx.send({ data: [] });
  } catch (error) {
    console.error('Error fetching features:', error);
    return ctx.internalServerError('Failed to fetch features');
  }
}
```

### Step 3: Backend Routes Update

**Template:**

```typescript
// backend/src/api/[COLLECTION]/routes/[COLLECTION].ts

export default {
  routes: [
    // Add enum options endpoint
    {
      method: 'GET',
      path: '/[COLLECTION]s/[FIELD_NAME]-options',
      handler: '[COLLECTION].get[FieldName]Options',
      config: {
        auth: false, // Adjust based on security needs
      },
    },
  ],
};
```

**Real Example:**

```typescript
// backend/src/api/property/routes/property.ts

{
  method: 'GET',
  path: '/properties/features-options',
  handler: 'property.getFeaturesOptions',
  config: {
    auth: false,
  },
}
```

### Step 4: Data Migration Script Template

**PowerShell Template:**

```powershell
# scripts/migrate-[FIELD_NAME]-to-enum.ps1

param(
    [string]$ApiUrl = "http://localhost:1337/api",
    [string]$AdminEmail = "<EMAIL>",
    [string]$AdminPassword = "your-admin-password",
    [string]$Collection = "[COLLECTION_NAME]",
    [string]$FieldName = "[FIELD_NAME]",
    [string]$EnumFieldName = "[FIELD_NAME]Enum"
)

# Define mapping from JSON values to enum values
$valueMapping = @{
    # Add your specific mappings here
    "Display Name 1" = "enum-value-1"
    "Display Name 2" = "enum-value-2"
    # ... more mappings
}

Write-Host "Starting migration for $Collection.$FieldName to $EnumFieldName"

# Login to get auth token
try {
    $loginResponse = Invoke-RestMethod -Uri "$ApiUrl/auth/local" -Method POST -Body (@{
        identifier = $AdminEmail
        password = $AdminPassword
    } | ConvertTo-Json) -ContentType "application/json"
    
    $authToken = $loginResponse.jwt
    Write-Host "✅ Authentication successful"
} catch {
    Write-Error "❌ Authentication failed: $($_.Exception.Message)"
    exit 1
}

# Fetch all records
try {
    $response = Invoke-RestMethod -Uri "$ApiUrl/$Collection`s?pagination[pageSize]=1000" -Headers @{
        Authorization = "Bearer $authToken"
    }
    
    $records = $response.data
    Write-Host "📊 Found $($records.Count) records to migrate"
} catch {
    Write-Error "❌ Failed to fetch records: $($_.Exception.Message)"
    exit 1
}

$successCount = 0
$errorCount = 0

foreach ($record in $records) {
    $oldValues = $record.attributes.$FieldName
    
    if ($oldValues -and $oldValues.Count -gt 0) {
        $enumValues = @()
        
        foreach ($value in $oldValues) {
            if ($valueMapping.ContainsKey($value)) {
                $enumValues += $valueMapping[$value]
            } else {
                Write-Warning "⚠️ Unknown value: '$value' for record $($record.attributes.title)"
            }
        }
        
        if ($enumValues.Count -gt 0) {
            $updateData = @{
                data = @{
                    $EnumFieldName = $enumValues
                }
            }
            
            try {
                Invoke-RestMethod -Uri "$ApiUrl/$Collection`s/$($record.documentId)" -Method PUT -Body ($updateData | ConvertTo-Json -Depth 3) -Headers @{
                    Authorization = "Bearer $authToken"
                    "Content-Type" = "application/json"
                }
                Write-Host "✅ Updated record: $($record.attributes.title)"
                $successCount++
            } catch {
                Write-Error "❌ Failed to update record: $($record.attributes.title) - $($_.Exception.Message)"
                $errorCount++
            }
        }
    }
}

Write-Host ""
Write-Host "📈 Migration Summary:"
Write-Host "   ✅ Successful updates: $successCount"
Write-Host "   ❌ Failed updates: $errorCount"
Write-Host "   📊 Total processed: $($records.Count)"

if ($errorCount -eq 0) {
    Write-Host "🎉 Migration completed successfully!"
} else {
    Write-Host "⚠️ Migration completed with $errorCount errors. Please review the logs."
}
```

### Step 5: Frontend Type Definitions

**Template:**

```typescript
// frontend/src/types/api.ts

export interface [CollectionName] extends StrapiEntity {
  // ... existing fields
  [fieldName]?: string[]; // Keep during transition
  [fieldName]Enum?: string[]; // New enum field
}

// Add option type for dynamic loading
export interface [FieldName]Option {
  value: string;
  label: string;
}
```

**Real Example:**

```typescript
// frontend/src/types/api.ts

export interface Property extends StrapiEntity {
  // ... existing fields
  features?: string[];
  featuresEnum?: string[];
}

export interface FeatureOption {
  value: string;
  label: string;
}
```

### Step 6: Frontend API Client Update

**Template:**

```typescript
// frontend/src/lib/api.ts

export const [collection]API = {
  // ... existing methods
  
  get[FieldName]Options: async (): Promise<{data: [FieldName]Option[]}> => {
    const response = await api.get(`/[collection]s/[field-name]-options`);
    return response.data;
  },
};
```

**Real Example:**

```typescript
// frontend/src/lib/api.ts

export const propertiesAPI = {
  // ... existing methods
  
  getFeaturesOptions: async (): Promise<{data: FeatureOption[]}> => {
    const response = await api.get('/properties/features-options');
    return response.data;
  },
};
```

### Step 7: Frontend Form Component Update

**Template:**

```typescript
// frontend/src/app/dashboard/[collection]/[id]/edit/page.tsx

import { [collection]API } from '@/lib/api';
import { [FieldName]Option } from '@/types/api';

const [available[FieldName]Options, setAvailable[FieldName]Options] = useState<[FieldName]Option[]>([]);
const [optionsLoading, setOptionsLoading] = useState(true);

// Load options dynamically
useEffect(() => {
  const fetchOptions = async () => {
    try {
      setOptionsLoading(true);
      const response = await [collection]API.get[FieldName]Options();
      setAvailable[FieldName]Options(response.data || []);
    } catch (error) {
      console.error('Error fetching [field name] options:', error);
      // Fallback to empty array or default options
      setAvailable[FieldName]Options([]);
    } finally {
      setOptionsLoading(false);
    }
  };

  fetchOptions();
}, []);

// Form field JSX
<div className="space-y-2">
  <label className="block text-sm font-medium text-gray-700">
    [Field Display Name]
  </label>
  {optionsLoading ? (
    <div className="animate-pulse">Loading options...</div>
  ) : (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
      {available[FieldName]Options.map((option) => (
        <label key={option.value} className="flex items-center space-x-2">
          <input
            type="checkbox" // or "radio" for single selection
            checked={formData.[fieldName]Enum?.includes(option.value) || false}
            onChange={(e) => {
              if (e.target.checked) {
                setFormData(prev => ({
                  ...prev,
                  [fieldName]Enum: [...(prev.[fieldName]Enum || []), option.value]
                }));
              } else {
                setFormData(prev => ({
                  ...prev,
                  [fieldName]Enum: (prev.[fieldName]Enum || []).filter(f => f !== option.value)
                }));
              }
            }}
            className="rounded border-gray-300"
          />
          <span className="text-sm text-gray-700">{option.label}</span>
        </label>
      ))}
    </div>
  )}
</div>
```

## 🧪 Testing Strategy

### Development Testing Checklist

- [ ] **Schema validation**: Enum field accepts only valid values
- [ ] **API endpoints**: New enum options endpoint returns correct data
- [ ] **Data migration**: All JSON data correctly converted to enum
- [ ] **Frontend forms**: Dynamic options loading works
- [ ] **Form submission**: New enum field saves correctly
- [ ] **Filtering**: Enum-based filtering functions properly
- [ ] **Backward compatibility**: Old JSON field still works during transition

### Production Testing Checklist

- [ ] **Database backup**: Complete backup before migration
- [ ] **Migration script**: Test on production copy first
- [ ] **API functionality**: All endpoints work with new field
- [ ] **Frontend compatibility**: All forms and filters work
- [ ] **Performance**: No degradation in query performance
- [ ] **Data integrity**: All migrated data is accurate

## 🔄 Rollback Procedures

### Immediate Rollback (Frontend Issues)

1. **Revert frontend code** to use hardcoded options
2. **Switch forms back** to use JSON field
3. **Update API calls** to use old field name

### Database Rollback (Data Issues)

```powershell
# Rollback script template
# scripts/rollback-[FIELD_NAME]-migration.ps1

# Restore from backup
pg_restore --clean --if-exists -d your_database backup_file.sql

# Or copy enum data back to JSON field
foreach ($record in $records) {
    $enumValues = $record.attributes.[fieldName]Enum
    if ($enumValues) {
        $jsonValues = $enumValues | ForEach-Object { 
            $reverseMapping[$_] 
        }
        
        # Update record with JSON values
        # ... update logic
    }
}
```

## 📊 Best Practices

### Performance Considerations

1. **Index enum fields** for better query performance
2. **Limit enum options** to reasonable numbers (< 50 options)
3. **Cache enum options** in frontend to reduce API calls
4. **Use pagination** for large datasets during migration

### Data Integrity

1. **Always backup** before schema changes
2. **Test migrations** on development data first
3. **Validate data** after migration completion
4. **Keep old field** during transition period
5. **Monitor logs** for migration errors

### User Experience

1. **Provide loading states** during option fetching
2. **Handle API errors** gracefully with fallbacks
3. **Maintain form state** during option loading
4. **Show clear error messages** for validation failures

### Code Maintainability

1. **Use consistent naming** conventions for enum fields
2. **Document enum values** and their meanings
3. **Create reusable components** for enum selection
4. **Centralize option formatting** logic

## 🎯 Real-World Examples

### Example 1: Property Features (Multiple Selection)

**Use Case**: Property features like "Swimming Pool", "Gym", "Garden"
**Field Type**: `enumeration` with `multiple: true`
**Frontend**: Checkbox grid for multiple selection

### Example 2: Property Status (Single Selection)

**Use Case**: Property status like "for-sale", "for-rent", "sold"
**Field Type**: `enumeration` with `multiple: false`
**Frontend**: Radio buttons or dropdown for single selection

### Example 3: Property Categories (Hierarchical)

**Use Case**: Property categories with subcategories
**Field Type**: `enumeration` with nested structure
**Frontend**: Nested dropdown or tree selection

## 🚀 Deployment Checklist

### Pre-Deployment

- [ ] Database backup completed
- [ ] Migration script tested on staging
- [ ] All tests passing
- [ ] Code review completed
- [ ] Documentation updated

### Deployment Steps

1. **Deploy backend changes** (schema + API)
2. **Run migration script** on production
3. **Validate migration** results
4. **Deploy frontend changes**
5. **Test critical user flows**
6. **Monitor error logs**

### Post-Deployment

- [ ] Verify all forms work correctly
- [ ] Check filtering functionality
- [ ] Monitor API performance
- [ ] Validate data integrity
- [ ] Remove old JSON field (after validation period)

## 📚 Additional Resources

- [Strapi v5 Content Types Documentation](https://docs.strapi.io/dev-docs/backend-customization/models)
- [Strapi Enumeration Field Guide](https://docs.strapi.io/dev-docs/backend-customization/models#enumeration)
- [Database Migration Best Practices](https://docs.strapi.io/dev-docs/migration/migration-guides)

---

**Note**: This guide provides a comprehensive template for converting any JSON field to enum in Strapi v5. Adapt the specific field names, collection names, and enum values to match your use case.