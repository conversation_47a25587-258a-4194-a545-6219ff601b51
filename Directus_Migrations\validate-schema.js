#!/usr/bin/env node

/**
 * Schema Validation Script for Enhanced Schema Definitions
 * Validates the new schema structure and reports any issues
 */

const { schemaDefinitions } = require('./schema-definitions-new.js');

class SchemaValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  validate() {
    console.log('🔍 Validating enhanced schema definitions...\n');
    
    this.validateCollections();
    this.validateFields();
    this.validateRelations();
    this.validateFieldNameConflicts();
    this.validateChoicesAndOptions();
    this.validateFileFields();
    
    this.printResults();
  }

  validateCollections() {
    console.log('📁 Validating collections...');
    
    const expectedCollections = [
      'memberships', 'projects', 'nearby_place_categories', 'properties', 
      'messages', 'notifications', 'properties_files', 'projects_files', 'messages_files'
    ];
    
    const actualCollections = Object.keys(schemaDefinitions.collections);
    
    for (const expected of expectedCollections) {
      if (actualCollections.includes(expected)) {
        console.log(`  ✅ Collection '${expected}' exists`);
      } else {
        this.errors.push(`Missing collection: ${expected}`);
      }
    }
    
    // Check for junction tables
    const junctionTables = ['properties_files', 'projects_files', 'messages_files'];
    for (const junction of junctionTables) {
      if (actualCollections.includes(junction)) {
        console.log(`  ✅ Junction table '${junction}' exists`);
      } else {
        this.errors.push(`Missing junction table: ${junction}`);
      }
    }
  }

  validateFields() {
    console.log('\n🔧 Validating fields...');
    
    const collections = Object.keys(schemaDefinitions.fields);
    
    for (const collection of collections) {
      const fields = schemaDefinitions.fields[collection];
      console.log(`  📝 Validating ${collection} (${fields.length} fields)`);
      
      // Check for required Directus core fields
      const isJunctionTable = collection.includes('_files');

      if (isJunctionTable) {
        // Junction tables only need basic fields
        const junctionFields = ['id'];
        for (const coreField of junctionFields) {
          const hasField = fields.some(f => f.field === coreField);
          if (hasField) {
            console.log(`    ✅ Junction field '${coreField}' exists`);
          } else {
            this.errors.push(`${collection}: Missing junction field '${coreField}'`);
          }
        }
      } else {
        // Regular collections need full core fields
        const coreFields = ['id', 'status', 'sort', 'user_created', 'date_created', 'user_updated', 'date_updated'];
        for (const coreField of coreFields) {
          const hasField = fields.some(f => f.field === coreField);
          if (hasField) {
            console.log(`    ✅ Core field '${coreField}' exists`);
          } else {
            this.errors.push(`${collection}: Missing core field '${coreField}'`);
          }
        }
      }
    }
  }

  validateRelations() {
    console.log('\n🔗 Validating relations...');
    
    const relations = schemaDefinitions.relations;
    console.log(`  Found ${relations.length} relations`);
    
    // Check for essential relations
    const essentialRelations = [
      'properties.owner_id -> directus_users',
      'properties.agent_id -> directus_users',
      'messages.sender_id -> directus_users',
      'messages.recipient_id -> directus_users',
      'notifications.recipient_id -> directus_users'
    ];
    
    for (const essential of essentialRelations) {
      const [collection_field, target] = essential.split(' -> ');
      const [collection, field] = collection_field.split('.');
      
      const hasRelation = relations.some(r => 
        r.collection === collection && 
        r.field === field && 
        r.related_collection === target
      );
      
      if (hasRelation) {
        console.log(`  ✅ Relation '${essential}' exists`);
      } else {
        this.errors.push(`Missing relation: ${essential}`);
      }
    }
  }

  validateFieldNameConflicts() {
    console.log('\n⚠️  Checking for field name conflicts...');
    
    const conflictChecks = [
      { collection: 'properties', oldField: 'status', newField: 'propertystatus' },
      { collection: 'properties', oldField: 'offer', newField: 'propertyoffer' },
      { collection: 'properties', oldField: 'features', newField: 'propertyfeatures' },
      { collection: 'projects', oldField: 'status', newField: 'projectstatus' },
      { collection: 'memberships', oldField: 'features', newField: 'membershipfeatures' }
    ];
    
    for (const check of conflictChecks) {
      const fields = schemaDefinitions.fields[check.collection] || [];
      const hasOldField = fields.some(f => f.field === check.oldField && f.field !== 'status'); // status is allowed as core field
      const hasNewField = fields.some(f => f.field === check.newField);
      
      if (hasOldField && check.oldField !== 'status') {
        this.warnings.push(`${check.collection}: Still using conflicting field name '${check.oldField}'`);
      }
      
      if (hasNewField) {
        console.log(`  ✅ ${check.collection}: Renamed '${check.oldField}' to '${check.newField}'`);
      } else if (check.oldField !== 'status') {
        this.errors.push(`${check.collection}: Missing renamed field '${check.newField}'`);
      }
    }
  }

  validateChoicesAndOptions() {
    console.log('\n🎯 Validating field choices and options...');
    
    const fieldsWithChoices = [];
    
    for (const [collection, fields] of Object.entries(schemaDefinitions.fields)) {
      for (const field of fields) {
        if (field.meta && field.meta.interface === 'select-dropdown') {
          if (field.meta.options && field.meta.options.choices) {
            fieldsWithChoices.push(`${collection}.${field.field}`);
            console.log(`  ✅ ${collection}.${field.field} has ${field.meta.options.choices.length} choices`);
          } else {
            this.errors.push(`${collection}.${field.field}: select-dropdown missing choices`);
          }
        }
      }
    }
    
    console.log(`  Found ${fieldsWithChoices.length} fields with proper choices`);
  }

  validateFileFields() {
    console.log('\n📁 Validating file fields...');
    
    const fileFields = [];
    
    for (const [collection, fields] of Object.entries(schemaDefinitions.fields)) {
      for (const field of fields) {
        if (field.type === 'alias' && field.meta && field.meta.special) {
          if (field.meta.special.includes('files') || field.meta.special.includes('file')) {
            fileFields.push(`${collection}.${field.field}`);
            console.log(`  ✅ ${collection}.${field.field} is a file field`);
          }
        }
      }
    }
    
    console.log(`  Found ${fileFields.length} file fields`);
    
    // Check for corresponding junction tables
    const expectedJunctions = ['properties_files', 'projects_files', 'messages_files'];
    for (const junction of expectedJunctions) {
      if (schemaDefinitions.fields[junction]) {
        console.log(`  ✅ Junction table '${junction}' has field definitions`);
      } else {
        this.errors.push(`Missing field definitions for junction table: ${junction}`);
      }
    }
  }

  printResults() {
    console.log('\n📊 Validation Results:');
    console.log('='.repeat(50));
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('🎉 Schema validation passed! No issues found.');
    } else {
      if (this.errors.length > 0) {
        console.log(`\n❌ Errors (${this.errors.length}):`);
        this.errors.forEach(error => console.log(`  - ${error}`));
      }
      
      if (this.warnings.length > 0) {
        console.log(`\n⚠️  Warnings (${this.warnings.length}):`);
        this.warnings.forEach(warning => console.log(`  - ${warning}`));
      }
    }
    
    console.log('\n📈 Schema Statistics:');
    console.log(`  Collections: ${Object.keys(schemaDefinitions.collections).length}`);
    console.log(`  Field definitions: ${Object.keys(schemaDefinitions.fields).length}`);
    console.log(`  Relations: ${schemaDefinitions.relations.length}`);
    
    const totalFields = Object.values(schemaDefinitions.fields).reduce((sum, fields) => sum + fields.length, 0);
    console.log(`  Total fields: ${totalFields}`);
  }
}

// Run validation
const validator = new SchemaValidator();
validator.validate();
