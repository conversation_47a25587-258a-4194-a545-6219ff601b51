#!/usr/bin/env node

/**
 * Comprehensive Directus Schema Migration Script
 * Migrates complete real estate platform schema from Strapi v5 to Directus
 * 
 * Usage: node scripts/migrate-to-directus.js [environment]
 * Example: node scripts/migrate-to-directus.js production
 */

const { createDirectus, rest, authentication, staticToken, readCollections, readFields, readRelations, createCollection, createField, createRelation, deleteCollection, deleteField, deleteRelation } = require('@directus/sdk');
const fs = require('fs').promises;
const path = require('path');
const readline = require('readline');

// Configuration
const CONFIG = {
  development: {
    url: 'http://localhost:8055',
    email: '<EMAIL>',
    password: 'Mb123321',
    token: null
  },
  staging: {
    url: process.env.DIRECTUS_STAGING_URL || 'https://staging-directus.yourdomain.com',
    email: process.env.DIRECTUS_STAGING_EMAIL,
    password: process.env.DIRECTUS_STAGING_PASSWORD,
    token: process.env.DIRECTUS_STAGING_TOKEN
  },
  production: {
    url: process.env.DIRECTUS_PROD_URL || 'https://directus.yourdomain.com',
    email: process.env.DIRECTUS_PROD_EMAIL,
    password: process.env.DIRECTUS_PROD_PASSWORD,
    token: process.env.DIRECTUS_PROD_TOKEN
  }
};

class DirectusMigrator {
  constructor(environment = 'development') {
    this.environment = environment;
    this.config = CONFIG[environment];
    this.directus = null;
    this.migrationLog = [];
    this.createdCollections = [];
    this.createdFields = [];
    this.createdRelations = [];
    
    if (!this.config) {
      throw new Error(`Invalid environment: ${environment}. Use: development, staging, or production`);
    }
  }

  async initialize() {
    console.log(`🚀 Initializing Directus migration for ${this.environment} environment...`);
    
    // Create Directus client
    this.directus = createDirectus(this.config.url).with(rest());
    
    // Authenticate
    if (this.config.token) {
      this.directus = this.directus.with(staticToken(this.config.token));
      console.log('✅ Using static token authentication');
    } else if (this.config.email && this.config.password) {
      this.directus = this.directus.with(authentication());
      await this.directus.login(this.config.email, this.config.password);
      console.log('✅ Authenticated with email/password');
    } else {
      throw new Error('No authentication method configured');
    }

    // Verify connection
    await this.verifyConnection();
  }

  async verifyConnection() {
    try {
      const collections = await this.directus.request(readCollections());
      console.log(`✅ Connected to Directus (${collections.length} existing collections)`);
    } catch (error) {
      throw new Error(`Failed to connect to Directus: ${error.message}`);
    }
  }

  async confirmMigration() {
    if (this.environment === 'production') {
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      const answer = await new Promise(resolve => {
        rl.question('⚠️  You are about to migrate to PRODUCTION. Are you sure? (yes/no): ', resolve);
      });

      rl.close();

      if (answer.toLowerCase() !== 'yes') {
        console.log('❌ Migration cancelled');
        process.exit(0);
      }
    }
  }

  async createBackup() {
    console.log('📦 Creating schema backup...');

    try {
      const collections = await this.directus.request(readCollections());
      const fields = await this.directus.request(readFields());
      const relations = await this.directus.request(readRelations());

      const backup = {
        timestamp: new Date().toISOString(),
        environment: this.environment,
        collections: collections,
        fields: fields,
        relations: relations
      };

      const backupPath = `backups/directus-backup-${this.environment}-${Date.now()}.json`;
      await fs.mkdir('backups', { recursive: true });
      await fs.writeFile(backupPath, JSON.stringify(backup, null, 2));

      console.log(`✅ Backup created: ${backupPath}`);
      return backupPath;
    } catch (error) {
      console.warn(`⚠️  Failed to create backup: ${error.message}`);
    }
  }

  async createCollection(collectionData) {
    try {
      console.log(`📁 Creating collection: ${collectionData.collection}`);

      const response = await this.directus.request(createCollection(collectionData));

      this.createdCollections.push(collectionData.collection);
      this.log(`Created collection: ${collectionData.collection}`);

      return response;
    } catch (error) {
      const errorMessage = error?.message || error?.toString() || 'Unknown error';
      if (errorMessage.includes('already exists') || errorMessage.includes('duplicate') || errorMessage.includes('unique')) {
        console.log(`⚠️  Collection ${collectionData.collection} already exists, skipping...`);
        return null;
      }
      throw new Error(`Failed to create collection ${collectionData.collection}: ${errorMessage}`);
    }
  }

  async createField(collection, fieldData) {
    try {
      const response = await this.directus.request(createField(collection, fieldData));

      this.createdFields.push(`${collection}.${fieldData.field}`);
      return response;
    } catch (error) {
      // Better error handling
      let errorMessage = 'Unknown error';
      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.errors && Array.isArray(error.errors)) {
        errorMessage = error.errors.map(e => e.message || e).join(', ');
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else {
        errorMessage = JSON.stringify(error, null, 2);
      }

      if (errorMessage.includes('already exists') || errorMessage.includes('duplicate') || errorMessage.includes('unique')) {
        console.log(`⚠️  Field ${collection}.${fieldData.field} already exists, skipping...`);
        return null;
      }

      console.error(`❌ Error creating field ${collection}.${fieldData.field}:`, error);
      throw new Error(`Failed to create field ${collection}.${fieldData.field}: ${errorMessage}`);
    }
  }

  async createRelation(relationData) {
    try {
      console.log(`🔗 Creating relation: ${relationData.collection}.${relationData.field} → ${relationData.related_collection}`);
      const response = await this.directus.request(createRelation(relationData));

      this.createdRelations.push(`${relationData.collection}.${relationData.field}`);
      this.log(`Created relation: ${relationData.collection}.${relationData.field}`);

      return response;
    } catch (error) {
      // Better error handling
      let errorMessage = 'Unknown error';
      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.errors && Array.isArray(error.errors)) {
        errorMessage = error.errors.map(e => e.message || e).join(', ');
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else {
        errorMessage = JSON.stringify(error, null, 2);
      }

      if (errorMessage.includes('already exists') || errorMessage.includes('duplicate') || errorMessage.includes('unique')) {
        console.log(`⚠️  Relation ${relationData.collection}.${relationData.field} already exists, skipping...`);
        return null;
      }

      console.error(`❌ Error creating relation ${relationData.collection}.${relationData.field}:`, error);
      console.error(`❌ Relation data:`, JSON.stringify(relationData, null, 2));
      throw new Error(`Failed to create relation: ${errorMessage}`);
    }
  }

  async extendUsersCollection() {
    console.log('👤 Extending directus_users collection...');
    
    const userExtensions = [
      {
        field: 'first_name',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 100,
          width: 'half'
        },
        schema: {
          max_length: 255,
          is_nullable: true
        }
      },
      {
        field: 'last_name',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 101,
          width: 'half'
        },
        schema: {
          max_length: 255,
          is_nullable: true
        }
      },
      {
        field: 'phone',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 102,
          width: 'half'
        },
        schema: {
          max_length: 50,
          is_nullable: true
        }
      },
      {
        field: 'company',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 103,
          width: 'half'
        },
        schema: {
          max_length: 255,
          is_nullable: true
        }
      },
      {
        field: 'job_title',
        type: 'string',
        meta: {
          interface: 'input',
          sort: 104,
          width: 'half'
        },
        schema: {
          max_length: 255,
          is_nullable: true
        }
      },
      {
        field: 'bio',
        type: 'text',
        meta: {
          interface: 'input-multiline',
          sort: 105,
          width: 'full'
        },
        schema: {
          is_nullable: true
        }
      },
      {
        field: 'is_agent',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          sort: 106,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          default_value: false
        }
      },
      {
        field: 'membership_id',
        type: 'uuid',
        meta: {
          interface: 'select-dropdown-m2o',
          display: 'related-values',
          display_options: {
            template: '{{name}}'
          },
          sort: 107,
          width: 'half'
        },
        schema: {
          is_nullable: true,
          foreign_key_table: 'memberships',
          foreign_key_column: 'id'
        }
      },
      {
        field: 'membership_expires_at',
        type: 'timestamp',
        meta: {
          interface: 'datetime',
          sort: 108,
          width: 'half'
        },
        schema: {
          is_nullable: true
        }
      }
    ];

    for (const field of userExtensions) {
      await this.createField('directus_users', field);
    }

    console.log('✅ User collection extended');
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}`;
    this.migrationLog.push(logEntry);
    console.log(`📝 ${message}`);
  }

  async saveMigrationLog() {
    const logPath = `logs/migration-${this.environment}-${Date.now()}.log`;
    await fs.mkdir('logs', { recursive: true });
    await fs.writeFile(logPath, this.migrationLog.join('\n'));
    console.log(`📋 Migration log saved: ${logPath}`);
  }

  async rollback() {
    console.log('🔄 Starting rollback...');

    // Delete created relations
    for (const relation of this.createdRelations.reverse()) {
      try {
        await this.directus.request(deleteRelation(relation));
        console.log(`🗑️  Deleted relation: ${relation}`);
      } catch (error) {
        console.warn(`⚠️  Failed to delete relation ${relation}: ${error.message}`);
      }
    }

    // Delete created fields
    for (const field of this.createdFields.reverse()) {
      try {
        const [collection, fieldName] = field.split('.');
        await this.directus.request(deleteField(collection, fieldName));
        console.log(`🗑️  Deleted field: ${field}`);
      } catch (error) {
        console.warn(`⚠️  Failed to delete field ${field}: ${error.message}`);
      }
    }

    // Delete created collections
    for (const collection of this.createdCollections.reverse()) {
      try {
        await this.directus.request(deleteCollection(collection));
        console.log(`🗑️  Deleted collection: ${collection}`);
      } catch (error) {
        console.warn(`⚠️  Failed to delete collection ${collection}: ${error.message}`);
      }
    }

    console.log('✅ Rollback completed');
  }
}

// Export for use in other scripts
module.exports = DirectusMigrator;

// Run if called directly
if (require.main === module) {
  const environment = process.argv[2] || 'development';
  
  const migrator = new DirectusMigrator(environment);
  
  migrator.initialize()
    .then(() => migrator.confirmMigration())
    .then(() => migrator.createBackup())
    .then(() => {
      console.log('🎯 Migration script initialized. Use migrator.migrate() to start migration.');
      console.log('📖 See migration-runner.js for complete migration execution.');
    })
    .catch(error => {
      console.error('❌ Migration initialization failed:', error.message);
      process.exit(1);
    });
}
